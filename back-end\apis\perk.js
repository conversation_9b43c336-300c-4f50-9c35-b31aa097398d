const { Router } = require("express");
const {
  getPerks,
  addPerk,
  getPerk,
  purchasePerk,
  confirmEscrow,
  initiatePerkChat,
  postReviews,
  releasePerk,
  refundPerk,
  reportTrade,
  getTradeDetails,
} = require("../controller/perk");
const { query,body  } = require('express-validator');
const { validate } = require("../middleware/validator");
const { authenticate } = require("../middleware/authenticate");
const { requireRefundAccess } = require("../middleware/roleAuth");
const router = Router();

router.get(
  "/getAll",
  [
    query("search")
      .optional()
      .isString()
      .withMessage("Search must be a string"),
    query("sort").optional().isString().withMessage("Sort must be a string"),
    query("order").optional().isString().withMessage("Order must be a string"),
    query("page")
      .optional()
      .isInt({ min: 0 })
      .withMessage("Page must be a non-negative integer"),
    query("pageSize")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Page size must be a positive integer"),
  ],
  validate,
  getPerks
);
router.post(
  "/addPerk",
  [
    body("name").notEmpty().withMessage("Name is required"),
    body("description").notEmpty().withMessage("Description is required"),
    body("price")
      .isFloat({ gt: 0 })
      .withMessage("Price must be a positive number"),
    body("userId").notEmpty().withMessage("User ID is required"),
    body("isLimited")
      .optional()
      .isBoolean()
      .withMessage("isLimited must be a boolean"),
    body("tokenAmount")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Token amount must be a positive integer"),
  ],
  validate,
  addPerk
);
router.post("/get/:id", getPerk);
router.post(
  "/buy",
  [
    body("userId").notEmpty().withMessage("User ID is required"),
    body("perkId").notEmpty().withMessage("Perk ID is required"),
    body("amount")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Amount must be an integer >= 1"),
    body("price").notEmpty().withMessage("Price is required"),
    body("extra").optional(),
    body("signature").optional(),
  ],
  validate,
  purchasePerk
);

router.post(
  "/confirmEscrow",
  [
    body("tradeId").notEmpty().withMessage("Trade ID is required"),
    body("txId").notEmpty().withMessage("Transaction ID is required"),
  ],
  validate,
  confirmEscrow
);

router.post(
  "/initiateChat",
  [
    body("userId").notEmpty().withMessage("User ID is required"),
    body("perkId").notEmpty().withMessage("Perk ID is required"),
  ],
  validate,
  initiatePerkChat
);

router.post(
  "/postReviews",
  authenticate,
  [
    body("userId").notEmpty().withMessage("User ID is required"),
    body("perkId").notEmpty().withMessage("Perk ID is required"),
    body("review").notEmpty().withMessage("Review text is required"),
    body("username").optional(),
    body("avatar").optional(),
  ],
  validate,
  postReviews
);

router.post(
  "/releasePerk",
  authenticate,
  // [
  //   body("tradeId").notEmpty().withMessage("Trade ID is required"),
  //   body("sellerWallet").notEmpty().withWith("Seller wallet is required"),
  // ],
  // validate,
  releasePerk
);

router.post(
  "/refundPerk",
  authenticate,
  requireRefundAccess,
  [
    body("tradeId").notEmpty().withMessage("Trade ID is required"),
    body("buyerWallet").notEmpty().withMessage("Buyer wallet is required"),
  ],
  validate,
  refundPerk
);

router.post(
  "/report",
  authenticate,
  [
    body("tradeId").notEmpty().withMessage("Trade ID is required"),
    body("reportReason").notEmpty().withMessage("Report reason is required"),
  ],
  validate,
  reportTrade
);

router.get(
  "/trade/:tradeId",
  authenticate,
  getTradeDetails
);

module.exports = router;

