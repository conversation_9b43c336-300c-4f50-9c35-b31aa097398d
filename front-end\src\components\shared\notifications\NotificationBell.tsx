"use client";

import React, { useState, useEffect, useRef } from 'react';
import { usePrivy } from "@privy-io/react-auth";
import { getNotifications, getUnreadNotificationCount, markNotificationAsRead, markAllNotificationsAsRead, releasePerk, refundPerk } from '@/axios/requests';
import { useGlobalModal } from '@/contexts/GlobalModalContext';
import { useWallet } from '@/hooks/useWallet';
import { useEscrowOperations } from '@/hooks/useEscrowOperations';
import ChatModal from '@/components/shared/chat/ChatModal';

interface Notification {
  id: number;
  type: string;
  title: string;
  message: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionUrl?: string;
  createdAt: string;
  data?: {
    tradeId?: number;
    disputeId?: number;
    buyerId?: number;
    sellerId?: number;
    [key: string]: any;
  };
}

type TabType = 'system' | 'trade';

export default function NotificationBell() {
  const [systemNotifications, setSystemNotifications] = useState<Notification[]>([]);
  const [tradeNotifications, setTradeNotifications] = useState<Notification[]>([]);
  const [systemUnreadCount, setSystemUnreadCount] = useState(0);
  const [tradeUnreadCount, setTradeUnreadCount] = useState(0);
  const [activeTab, setActiveTab] = useState<TabType>('system');
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user } = usePrivy();
  const { openModal, closeModal } = useGlobalModal();
  const { solanaWallet, isConnected } = useWallet();
  const escrowOperations = useEscrowOperations();

  // Load notifications and separate them by type
  const loadNotifications = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const [notificationsResponse, unreadResponse] = await Promise.all([
        getNotifications({ page: 1, pageSize: 20 }),
        getUnreadNotificationCount()
      ]);

      if (notificationsResponse.status === 200) {
        const allNotifications = notificationsResponse.data.notifications;

        // Separate notifications into system and trade categories
        const systemNots: Notification[] = [];
        const tradeNots: Notification[] = [];

        allNotifications.forEach((notification: Notification) => {
          if (isTradeNotification(notification)) {
            tradeNots.push(notification);
          } else {
            systemNots.push(notification);
          }
        });

        setSystemNotifications(systemNots);
        setTradeNotifications(tradeNots);

        // Calculate unread counts for each category
        const systemUnread = systemNots.filter(n => !n.isRead).length;
        const tradeUnread = tradeNots.filter(n => !n.isRead).length;

        setSystemUnreadCount(systemUnread);
        setTradeUnreadCount(tradeUnread);
      }

      // Note: We calculate unread counts from the filtered notifications
      // rather than using the API response since we need separate counts
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to determine if a notification is trade-related
  const isTradeNotification = (notification: Notification): boolean => {
    const tradeTypes = [
      'trade_completed',
      'trade_refunded',
      'dispute_created',
      'dispute_assigned',
      'dispute_resolved',
      'perk_purchased',
      'perk_sold',
      'escrow_created',
      'escrow_released',
      'trade_reported'
    ];

    // Check if it's a trade-related type
    if (tradeTypes.includes(notification.type)) {
      // Additional check: ensure the user is involved in the trade
      if (notification.data?.buyerId || notification.data?.sellerId) {
        const userId = user?.id;
        const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;
        return notification.data.buyerId === userIdNumber || notification.data.sellerId === userIdNumber;
      }
      return true; // If no specific user data, assume it's relevant
    }

    return false;
  };

  // Get user role in trade for display
  const getUserRoleInTrade = (notification: Notification): string => {
    if (!notification.data?.buyerId || !notification.data?.sellerId) return '';

    const userId = user?.id;
    const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;

    if (notification.data.buyerId === userIdNumber) return 'buyer';
    if (notification.data.sellerId === userIdNumber) return 'seller';
    return '';
  };

  // Helper function to determine if notification should open chat modal
  const shouldOpenChatModal = (notification: Notification): boolean => {
    const chatTypes = [
      'perk_sold', 'perk_purchased', 'escrow_released', 'trade_completed',
      'trade_disputed', 'dispute_resolved', 'trade_refunded', 'chat_message',
      'escrow_created', 'escrow_release_reminder'
    ];
    return chatTypes.includes(notification.type) && !!notification.data?.chatRoomId;
  };

  // Create release function for escrow
  const createReleaseFunction = (notification: Notification) => {
    return async () => {
      if (!notification.data?.tradeId || !solanaWallet?.address || !isConnected) {
        console.error('❌ [NotificationBell] Missing data for release:', {
          tradeId: notification.data?.tradeId,
          wallet: solanaWallet?.address,
          connected: isConnected
        });
        return;
      }

      try {
        console.log('🔍 [NotificationBell] Starting escrow release from notification');

        // We need to get the trade details to perform the release
        // For now, we'll need to navigate to the perk page where the full release logic exists
        const perkId = notification.data?.perkId;
        if (perkId) {
          window.location.href = `/perks-shop/${perkId}?tradeId=${notification.data.tradeId}&openChat=1&chatRoomId=${notification.data.chatRoomId}`;
        }
      } catch (error) {
        console.error('❌ [NotificationBell] Release failed:', error);
      }
    };
  };

  // Create refund function for escrow
  const createRefundFunction = (notification: Notification) => {
    return async () => {
      if (!notification.data?.tradeId || !solanaWallet?.address || !isConnected) {
        console.error('❌ [NotificationBell] Missing data for refund:', {
          tradeId: notification.data?.tradeId,
          wallet: solanaWallet?.address,
          connected: isConnected
        });
        return;
      }

      try {
        console.log('🔍 [NotificationBell] Starting escrow refund from notification');

        // Navigate to the perk page where the full refund logic exists
        const perkId = notification.data?.perkId;
        if (perkId) {
          window.location.href = `/perks-shop/${perkId}?tradeId=${notification.data.tradeId}&openChat=1&chatRoomId=${notification.data.chatRoomId}`;
        }
      } catch (error) {
        console.error('❌ [NotificationBell] Refund failed:', error);
      }
    };
  };

  // Function to open chat modal
  const openChatModal = (notification: Notification) => {
    if (!notification.data?.chatRoomId) {
      console.error('❌ [NotificationBell] No chatRoomId in notification data');
      return;
    }

    // Get current user id from localStorage
    const userBoStr = typeof window !== "undefined" ? localStorage.getItem("userBo") : null;
    const userBo = userBoStr ? JSON.parse(userBoStr) : null;
    const myUserId = userBo?.id;

    // Determine the other party (buyer or seller)
    const userId = user?.id;
    const userIdNumber = typeof userId === 'string' ? parseInt(userId) : userId;

    let buyerId = notification.data.buyerId;
    let sellerId = notification.data.sellerId;

    // Special handling for escrow_release_reminder - seller is the current user
    if (notification.type === 'escrow_release_reminder') {
      sellerId = userIdNumber; // Current user is the seller
      buyerId = notification.data.buyerId; // Buyer from notification data
    }
    // If we don't have both IDs, try to determine from the notification
    else if (!buyerId || !sellerId) {
      if (notification.data.buyerId === userIdNumber) {
        buyerId = userIdNumber;
        sellerId = notification.data.sellerId || notification.data.receiverId;
      } else {
        sellerId = userIdNumber;
        buyerId = notification.data.buyerId || notification.data.senderId;
      }
    }

    console.log('🔍 [NotificationBell] Opening chat modal with:', {
      chatRoomId: notification.data.chatRoomId,
      buyerId,
      sellerId,
      myUserId,
      userIdNumber
    });

    // Debug notification data
    console.log('🔍 [NotificationBell] Opening chat modal with data:', {
      notificationType: notification.type,
      notificationData: notification.data,
      buyerId: buyerId || myUserId,
      sellerId: sellerId || myUserId,
      myUserId,
      currentUser: user?.id
    });

    // Open ChatModal in global modal
    openModal({
      id: `chat-modal-${notification.data.chatRoomId}`,
      component: React.createElement(ChatModal, {
        chatRoomId: notification.data.chatRoomId,
        buyerId: buyerId || myUserId,
        sellerId: sellerId || myUserId,
        onClose: () => closeModal(`chat-modal-${notification.data.chatRoomId}`),
        onRelease: createReleaseFunction(notification),
        onRefund: createRefundFunction(notification),
        onReport: () => {},
        activeTrade: notification.data.tradeId ? {
          id: notification.data.tradeId,
          status: 'escrowed', // Assume escrowed status for notifications
          tradeId: notification.data.tradeId,
          from: notification.data.buyerId,
          to: notification.data.sellerId
        } : undefined
      }),
      closeOnBackdropClick: false,
      closeOnEscape: true,
      preventClose: false,
      zIndex: 9999,
      backdropClassName: 'bg-black/50 backdrop-blur-sm',
      modalClassName: '',
      disableScroll: true
    });

    // Close the notification dropdown
    setIsOpen(false);
  };

  // Load notifications on mount and when user changes
  useEffect(() => {
    loadNotifications();
  }, [user]);

  // Refresh notifications every 30 seconds
  useEffect(() => {
    const interval = setInterval(loadNotifications, 30000);
    return () => clearInterval(interval);
  }, [user]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Mark notification as read
  const handleNotificationClick = async (notification: Notification) => {
    console.log('🔍 [NotificationBell] Notification clicked:', {
      type: notification.type,
      shouldOpenChat: shouldOpenChatModal(notification),
      chatRoomId: notification.data?.chatRoomId,
      actionUrl: notification.actionUrl
    });

    if (!notification.isRead) {
      try {
        await markNotificationAsRead(notification.id);

        // Update the appropriate notification list
        if (isTradeNotification(notification)) {
          setTradeNotifications(prev =>
            prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n)
          );
          setTradeUnreadCount(prev => Math.max(0, prev - 1));
        } else {
          setSystemNotifications(prev =>
            prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n)
          );
          setSystemUnreadCount(prev => Math.max(0, prev - 1));
        }
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }

    // Check if this notification should open chat modal instead of navigating
    if (shouldOpenChatModal(notification)) {
      console.log('✅ [NotificationBell] Opening chat modal for notification');
      openChatModal(notification);
    } else if (notification.actionUrl) {
      // Navigate to action URL if provided and not a chat notification
      console.log('🔍 [NotificationBell] Navigating to actionUrl:', notification.actionUrl);
      window.location.href = notification.actionUrl;
    }
  };

  // Mark all as read for current tab
  const handleMarkAllAsRead = async () => {
    try {
      await markAllNotificationsAsRead();

      // Update both notification lists
      setSystemNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      setTradeNotifications(prev => prev.map(n => ({ ...n, isRead: true })));

      // Reset unread counts
      setSystemUnreadCount(0);
      setTradeUnreadCount(0);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  // Mark all as read for specific tab
  const handleMarkTabAsRead = async (tabType: TabType) => {
    try {
      // Note: This would ideally be a more specific API call
      // For now, we'll mark individual notifications as read
      const notifications = tabType === 'system' ? systemNotifications : tradeNotifications;
      const unreadNotifications = notifications.filter(n => !n.isRead);

      for (const notification of unreadNotifications) {
        await markNotificationAsRead(notification.id);
      }

      // Update the appropriate list
      if (tabType === 'system') {
        setSystemNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
        setSystemUnreadCount(0);
      } else {
        setTradeNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
        setTradeUnreadCount(0);
      }
    } catch (error) {
      console.error(`Failed to mark ${tabType} notifications as read:`, error);
    }
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'dispute_created':
      case 'dispute_assigned':
      case 'dispute_resolved':
        return (
          <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        );
      case 'trade_completed':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'trade_refunded':
        return (
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
        );
      case 'moderator_added':
      case 'moderator_removed':
        return (
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
        );
      case 'system_announcement':
        return (
          <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  // Don't render if user is not authenticated
  if (!user) return null;

  const totalUnreadCount = systemUnreadCount + tradeUnreadCount;
  const currentNotifications = activeTab === 'system' ? systemNotifications : tradeNotifications;
  const currentUnreadCount = activeTab === 'system' ? systemUnreadCount : tradeUnreadCount;

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Notification Bell Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>

        {/* Combined Unread Count Badge */}
        {totalUnreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
              {totalUnreadCount > 0 && (
                <button
                  onClick={handleMarkAllAsRead}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Mark all read
                </button>
              )}
            </div>

            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('system')}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'system'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                System
                {systemUnreadCount > 0 && (
                  <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                    {systemUnreadCount}
                  </span>
                )}
              </button>
              <button
                onClick={() => setActiveTab('trade')}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'trade'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Trades
                {tradeUnreadCount > 0 && (
                  <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                    {tradeUnreadCount}
                  </span>
                )}
              </button>
            </div>

            {/* Tab-specific Mark as Read */}
            {currentUnreadCount > 0 && (
              <div className="mt-2 flex justify-end">
                <button
                  onClick={() => handleMarkTabAsRead(activeTab)}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  Mark {activeTab} as read
                </button>
              </div>
            )}
          </div>

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-2">Loading notifications...</p>
              </div>
            ) : currentNotifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5-5V9a6 6 0 10-12 0v3l-5 5h5m7 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                <p>No {activeTab} notifications yet</p>
                <p className="text-xs text-gray-400 mt-1">
                  {activeTab === 'system'
                    ? 'System announcements and updates will appear here'
                    : 'Trade-related notifications will appear here'
                  }
                </p>
              </div>
            ) : (
              currentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification)}
                  className={`p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors ${
                    !notification.isRead ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {getNotificationIcon(notification.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start">
                        <p className={`text-sm font-medium ${
                          !notification.isRead ? 'text-gray-900' : 'text-gray-700'
                        }`}>
                          {notification.title}
                        </p>
                        {!notification.isRead && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1"></div>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-xs text-gray-400">
                          {formatTimeAgo(notification.createdAt)}
                        </p>
                        <div className="flex items-center space-x-2">
                          {/* Show user role for trade notifications */}
                          {activeTab === 'trade' && getUserRoleInTrade(notification) && (
                            <span className={`text-xs px-2 py-0.5 rounded ${
                              getUserRoleInTrade(notification) === 'buyer'
                                ? 'text-green-600 bg-green-100'
                                : 'text-purple-600 bg-purple-100'
                            }`}>
                              {getUserRoleInTrade(notification)}
                            </span>
                          )}
                          {/* Show trade ID for trade notifications */}
                          {activeTab === 'trade' && notification.data?.tradeId && (
                            <span className="text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded">
                              Trade #{notification.data.tradeId}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          {(systemNotifications.length > 0 || tradeNotifications.length > 0) && (
            <div className="px-4 py-3 border-t border-gray-200">
              <button
                onClick={() => {
                  setIsOpen(false);
                  // Navigate to full notifications page if you have one
                  // window.location.href = '/notifications';
                }}
                className="w-full text-center text-sm text-blue-600 hover:text-blue-800"
              >
                View all {activeTab} notifications
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
