import * as anchor from "@coral-xyz/anchor";
import { Program } from "@coral-xyz/anchor";
import { Funhi } from "../target/types/funhi";
import {
  Keypair,
  PublicKey,
  SystemProgram,
  LAMPORTS_PER_SOL,
} from "@solana/web3.js";
import { expect } from "chai";
import {
  ASSOCIATED_TOKEN_PROGRAM_ID,
  getAssociatedTokenAddressSync,
  getAccount,
  TOKEN_PROGRAM_ID,
  getOrCreateAssociatedTokenAccount,
  createAssociatedTokenAccountInstruction,
} from "@solana/spl-token";

async function getTokenBalance(
  provider: anchor.Provider,
  ata: PublicKey
): Promise<number> {
  try {
    const account = await getAccount(provider.connection, ata);
    return Number(account.amount);
  } catch (e) {
    return 0;
  }
}

describe("funhi", () => {
  const provider = anchor.AnchorProvider.env();
  anchor.setProvider(provider);

  const program = anchor.workspace.funhi as Program<Funhi>;
  const authority = (provider.wallet as anchor.Wallet).payer;

  // Define all keypairs and constants
  const feeRecipient = Keypair.generate();
  const creator = Keypair.generate();
  const buyer = Keypair.generate();
  const seller = Keypair.generate();
  const secondBuyer = Keypair.generate();
  const unauthorizedUser = Keypair.generate();

  const initialVirtualTokenReserves = new anchor.BN(1_073_000_191);
  const initialVirtualSolReserves = new anchor.BN(30 * LAMPORTS_PER_SOL);
  const tokenTotalSupply = new anchor.BN(1_000_000_000 * 1e6);
  const creatorVaultAmount = new anchor.BN(10_000_000 * 1e6);
  const initialRealTokenReserves = tokenTotalSupply.sub(creatorVaultAmount);

  const graduationThreshold = new anchor.BN(2 * LAMPORTS_PER_SOL); // Lower for testing
  const tradingFeeBps = new anchor.BN(100); // 1%
  const firstBuyFeeSol = new anchor.BN(0.02 * LAMPORTS_PER_SOL);

  // PDAs and Keypairs
  let global: PublicKey;
  const mint = Keypair.generate();
  let bondingCurve: PublicKey;
  let creatorVault: PublicKey;
  let bondingCurveAta: PublicKey;
  let creatorVaultAta: PublicKey;
  let lastWithdraw: PublicKey;
  let perkMint: Keypair;
  before(async () => {
    // Fund all wallets
    const fundTx = new anchor.web3.Transaction()
      .add(
        SystemProgram.transfer({
          fromPubkey: authority.publicKey,
          toPubkey: buyer.publicKey,
          lamports: 5 * LAMPORTS_PER_SOL,
        })
      )
      .add(
      SystemProgram.transfer({
        fromPubkey: authority.publicKey,
        toPubkey: seller.publicKey,
        lamports: 5 * LAMPORTS_PER_SOL,
      })
    )
      .add(
        SystemProgram.transfer({
          fromPubkey: authority.publicKey,
          toPubkey: secondBuyer.publicKey,
          lamports: 5 * LAMPORTS_PER_SOL,
        })
      )
      .add(
        SystemProgram.transfer({
          fromPubkey: authority.publicKey,
          toPubkey: creator.publicKey,
          lamports: 2 * LAMPORTS_PER_SOL,
        })
      );
    await provider.sendAndConfirm(fundTx);

    // Derive all PDAs
    [global] = PublicKey.findProgramAddressSync(
      [Buffer.from("global")],
      program.programId
    );
    [bondingCurve] = PublicKey.findProgramAddressSync(
      [Buffer.from("bonding_curve"), mint.publicKey.toBuffer()],
      program.programId
    );
    [creatorVault] = PublicKey.findProgramAddressSync(
      [
        Buffer.from("creator_vault"),
        creator.publicKey.toBuffer(),
        mint.publicKey.toBuffer(),
      ],
      program.programId
    );
    [lastWithdraw] = PublicKey.findProgramAddressSync(
      [Buffer.from("last_withdraw")],
      program.programId
    );
    bondingCurveAta = getAssociatedTokenAddressSync(
      mint.publicKey,
      bondingCurve,
      true
    );
    creatorVaultAta = getAssociatedTokenAddressSync(
      mint.publicKey,
      creatorVault,
      true
    );
  });


  it("Is initialized!", async () => {
    await program.methods
      .initialize(
        initialVirtualTokenReserves,
        initialVirtualSolReserves,
        initialRealTokenReserves,
        tokenTotalSupply,
        tradingFeeBps,
        feeRecipient.publicKey,
        firstBuyFeeSol,
        graduationThreshold
      )
      .rpc();

    const globalData = await program.account.global.fetch(global);
    expect(globalData.authority.toBase58()).to.eq(
      authority.publicKey.toBase58()
    );
    expect(globalData.firstBuyFeeSol.toString()).to.eq(
      firstBuyFeeSol.toString()
    );
  });

  it("Creates a new token, bonding curve, and creator vault", async () => {
    await program.methods
      .create("Test Token", "TEST", "https://test.com/token.json")
      .accounts({
        signer: authority.publicKey,
        creator: creator.publicKey,
        feeRecipient: feeRecipient.publicKey,
        global,
        mint: mint.publicKey,
        tokenProgram: TOKEN_PROGRAM_ID,
      })
      .signers([mint])
      .rpc();

    const bondingCurveData = await program.account.bondingCurve.fetch(
      bondingCurve
    );
    expect(bondingCurveData.mint.toBase58()).to.eq(mint.publicKey.toBase58());
    expect(bondingCurveData.realSolReserves.toNumber()).to.eq(0);
  });

  it("Allows the FIRST buy and charges the fixed fee", async () => {
    const solAmountToBuy = new anchor.BN(1 * LAMPORTS_PER_SOL);
    const buyerAta = getAssociatedTokenAddressSync(
      mint.publicKey,
      buyer.publicKey
    );

    await program.methods
      .buy(solAmountToBuy, new anchor.BN(0))
      .accounts({
        signer: buyer.publicKey,
        mint: mint.publicKey,
        creator: creator.publicKey,
        feeRecipient: feeRecipient.publicKey,
        tokenProgram: TOKEN_PROGRAM_ID,
      })
      .signers([buyer])
      .rpc();

    const curveData = await program.account.bondingCurve.fetch(bondingCurve);
    const expectedSolInCurve = solAmountToBuy.sub(firstBuyFeeSol);
    expect(curveData.realSolReserves.toString()).to.eq(
      expectedSolInCurve.toString()
    );
  });

  it("Escrow buy, that is the first step of escrow creation, iniated by the buyer ", async () => {
        perkMint = Keypair.generate();


    const buyerAta = getAssociatedTokenAddressSync(
      mint.publicKey,
      buyer.publicKey
    );
  // Create the perk mint account
  await program.methods
    .create("Perk Token", "PERK", "https://test.com/perk.json")
    .accounts({
      signer: authority.publicKey,
      creator: creator.publicKey,
      feeRecipient: feeRecipient.publicKey,
      global,
      mint: perkMint.publicKey,
      tokenProgram: TOKEN_PROGRAM_ID,
    })
    .signers([perkMint])
    .rpc();

  const initialTokenBalance = await getTokenBalance(provider, buyerAta);
  const perk_token_amount = new anchor.BN(
        Math.floor(initialTokenBalance / 2)
  );
    const purchase_token_amount = perk_token_amount;

  const id = new anchor.BN(1);

  console.log("program ID", program.programId.toBase58());
  console.log("ID buffer:", id.toArrayLike(Buffer, "le", 8));

  try {
    await program.methods
      .escrowBuy(id, purchase_token_amount, perk_token_amount)
      .accounts({
        signer: buyer.publicKey,
        purchaseTokenMint: mint.publicKey, //TODO res
        perkTokenMint: perkMint.publicKey,
        tokenProgram: TOKEN_PROGRAM_ID,
      })
      .signers([buyer])
      .rpc();
  } catch(error) {
    console.log("Full error: ", error);
    if (error.logs) {
      console.log("Transaction logs:", error.logs);
    
  } 
}     
});


it("Buy escrow, then release ", async () => {
  perkMint = Keypair.generate();
  console.log("bug escrow then release started");

  const buyerAta = getAssociatedTokenAddressSync(
            mint.publicKey,
            buyer.publicKey
      );
  // Create the perk mint account
  await program.methods
    .create("Perk Token", "PERK", "https://test.com/perk.json")
    .accounts({
      signer: authority.publicKey,
      creator: creator.publicKey,
      feeRecipient: feeRecipient.publicKey,
      global,
      mint: perkMint.publicKey,
      tokenProgram: TOKEN_PROGRAM_ID,
    })
    .signers([perkMint])
    .rpc();

  // Give the seller some perk tokens
  const sellerPerkAta = getAssociatedTokenAddressSync(
    perkMint.publicKey,
    seller.publicKey
  );

  // Create seller's perk token account and mint tokens to them
  const createAndMintToSellerTx = await program.methods
    .buy(new anchor.BN(0.1 * LAMPORTS_PER_SOL), new anchor.BN(0)) // Buy some perk tokens
    .accounts({
      signer: seller.publicKey,
      mint: perkMint.publicKey,
      creator: creator.publicKey,
      feeRecipient: feeRecipient.publicKey,
      tokenProgram: TOKEN_PROGRAM_ID,
    })
    .signers([seller])
    .rpc();
  console.log("✅ Seller now has perk tokens");

  const initialTokenBalance = await getTokenBalance(provider, buyerAta);
  const purchase_token_amount = new anchor.BN(Math.floor(initialTokenBalance / 4));
  const perk_token_amount = new anchor.BN(1000000); 

  console.log("🔍 DEBUG: Initial token balance:", initialTokenBalance); 
  console.log("🔍 DEBUG: Purchase amount:", purchase_token_amount.toString());
  console.log("🔍 DEBUG: Perk amount:", perk_token_amount.toString());

  const id = new anchor.BN(2);

  // Derive the required accounts
  const [purchasePda] = PublicKey.findProgramAddressSync(
    [Buffer.from("purchase"), id.toArrayLike(Buffer, "le", 8)],
    program.programId
  );


  console.log("program ID", program.programId.toBase58());
  console.log("ID buffer:", id.toArrayLike(Buffer, "le", 8));

  try {
    await program.methods
      .escrowBuy(id, purchase_token_amount, perk_token_amount)
      .accounts({
        signer: buyer.publicKey,
        purchaseTokenMint: mint.publicKey,
        perkTokenMint: perkMint.publicKey,
        tokenProgram: TOKEN_PROGRAM_ID,
      })
        .signers([buyer])
        .rpc();
            console.log("✅ EscrowBuy succeeded!");

  } catch(error) {
        console.log("❌ EscrowBuy failed:", error.message);

    if (error.logs) {
      console.log("buy: Transaction logs:", error.logs);
    }
    return; 
  }

  // Verify the purchase account exists
  try {
    const purchaseAccountData = await program.account.purchase.fetch(purchasePda);
    console.log("✅ Purchase account found:", purchaseAccountData);
  } catch (error) {
    console.log("❌ Purchase account not found:", error.message);
    return;
  }
  //  mimic the seller releasing the escrow after providing service
  console.log("Seller releasing escrow sell");
  // get purchase account created in escrowBuy instruction above
  let purchaseAccount = getAssociatedTokenAddressSync(
    mint.publicKey,
    buyer.publicKey,
    true
  );

const sellerPurchaseTokenAta = getAssociatedTokenAddressSync(
  mint.publicKey,
  seller.publicKey 
);

const buyerPerkTokenAta = getAssociatedTokenAddressSync(
  perkMint.publicKey,
  buyer.publicKey
);

const sellerPerkTokenAta = getAssociatedTokenAddressSync(
  perkMint.publicKey,
  seller.publicKey 
);

const vaultAta = getAssociatedTokenAddressSync(
  mint.publicKey,
  purchasePda,
  true  // allowOwnerOffCurve for PDA
);
  try {
    await program.methods
    .escrowSell(id)
    .accounts({
      seller: seller.publicKey,
      buyer: buyer.publicKey,
      purchaseTokenMint: mint.publicKey,
      perkTokenMint: perkMint.publicKey,
      purchaseTokenAta: sellerPurchaseTokenAta,
      perkTokenAta: sellerPerkTokenAta,
      perkTokenDestinationAta: buyerPerkTokenAta,
      purchase: purchasePda,
      vault: vaultAta,
      associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
      tokenProgram: TOKEN_PROGRAM_ID,
      systemProgram: SystemProgram.programId,
    })
    .signers([seller])
    .rpc();
    
  } catch(error) {
    console.log("Full error: ", error);
    if (error.logs) {
      console.log("Transaction logs:", error.logs);
    }
  }
  // make sure the seller received the tokens, the received token sh

  const sellerTokenBalance = await getTokenBalance(provider, sellerPurchaseTokenAta);
  console.log("Seller's token balance after escrow sell:", sellerTokenBalance);
  // make sure the sellerTokenBalance equal the balance of the purchase token amount
  expect(sellerTokenBalance).to.be.eq(purchase_token_amount.toNumber());
  
  // make sure the buyer received the perk tokens
  const buyerPerkBalance = await getTokenBalance(provider, buyerPerkTokenAta);
  console.log("Buyer's perk token balance after escrow sell:", buyerPerkBalance);
  // make sure the buyerPerkBalance equal the perk token amount
  expect(buyerPerkBalance).to.be.eq(perk_token_amount.toNumber());
  // make sure the vault is empty
  const vaultBalance = await getTokenBalance(provider, vaultAta);
  console.log("Vault balance after escrow sell:", vaultBalance);
  expect(vaultBalance).to.be.eq(0);
  // make sure the purchase account is closed
  try {
    const purchaseAccountData = await program.account.purchase.fetch(purchasePda);
    console.log("❌ Purchase account still exists:", purchaseAccountData);
  } catch (error) {
    console.log("✅ Purchase account closed as expected:", error.message);    
  }
  // make sure the perk token account is closed
  try {
    const perkAccountData = await getAccount(provider.connection, perkMint.publicKey);
    console.log("❌ Perk token account still exists:", perkAccountData);
  } catch (error) {
    console.log("✅ Perk token account closed as expected:", error.message);
  }
  });
  it("Buy escrow, then refund ", async () => {
  perkMint = Keypair.generate();
  console.log("bug escrow then release started");

  const buyerAta = getAssociatedTokenAddressSync(
            mint.publicKey,
            buyer.publicKey
  );
  // Create the perk mint account
  await program.methods
    .create("Perk Token", "PERK", "https://test.com/perk.json")
    .accounts({
      signer: authority.publicKey,
      creator: creator.publicKey,
      feeRecipient: feeRecipient.publicKey,
      global,
      mint: perkMint.publicKey,
      tokenProgram: TOKEN_PROGRAM_ID,
    })
    .signers([perkMint])
    .rpc();


  const initialTokenBalance = await getTokenBalance(provider, buyerAta);
  const purchase_token_amount = new anchor.BN(Math.floor(initialTokenBalance / 4));
  const perk_token_amount = new anchor.BN(1000000); 

  console.log("🔍 DEBUG: Initial token balance:", initialTokenBalance); 
  console.log("🔍 DEBUG: Purchase amount:", purchase_token_amount.toString());
  console.log("🔍 DEBUG: Perk amount:", perk_token_amount.toString());

  const id = new anchor.BN(4);

  // Derive the required accounts
  const [purchasePda] = PublicKey.findProgramAddressSync(
    [Buffer.from("purchase"), id.toArrayLike(Buffer, "le", 8)],
    program.programId
  );

  console.log("program ID", program.programId.toBase58());
  console.log("ID buffer:", id.toArrayLike(Buffer, "le", 8));

  try {
    await program.methods
      .escrowBuy(id, purchase_token_amount, perk_token_amount)
      .accounts({
        signer: buyer.publicKey,
        purchaseTokenMint: mint.publicKey,
        perkTokenMint: perkMint.publicKey,
        tokenProgram: TOKEN_PROGRAM_ID,
      })
        .signers([buyer])
        .rpc();
            console.log("✅ EscrowBuy succeeded!");

  } catch(error) {
        console.log("❌ EscrowBuy failed:", error.message);

    if (error.logs) {
      console.log("buy: Transaction logs:", error.logs);
    }
    return; 
  }

  // Verify the purchase account exists
  try {
    const purchaseAccountData = await program.account.purchase.fetch(purchasePda);
    console.log("✅ Purchase account found:", purchaseAccountData);
  } catch (error) {
    console.log("❌ Purchase account not found:", error.message);
    return;
  }

  //  refund the buyer
  console.log("Refunding the buyer");
  // get purchase account created in escrowBuy instruction above
  let purchaseAccount = getAssociatedTokenAddressSync(
    mint.publicKey,
    buyer.publicKey,
    true
  );

const vaultAta = getAssociatedTokenAddressSync(
  mint.publicKey,
  purchasePda,
  true  // allowOwnerOffCurve for PDA
);
  try {
    await program.methods
    .escrowRefund(id)
    .accounts({
      buyer: buyer.publicKey,
      purchaseTokenMint: mint.publicKey,
      buyerTokenAta: purchaseAccount,
      purchase: purchasePda,
      vault: vaultAta,
      tokenProgram: TOKEN_PROGRAM_ID,
      systemProgram: SystemProgram.programId,
    })
    .signers([buyer])
    .rpc();
    
  } catch(error) {
    console.log("Full error: ", error);
    if (error.logs) {
      console.log("Transaction logs:", error.logs);
    }
  }
  });

  it("make sure buying with insufficient funds fails", async () => {
  perkMint = Keypair.generate();
  const buyerAta = getAssociatedTokenAddressSync(
            mint.publicKey,
            buyer.publicKey
      );
  // Create the perk mint account
  await program.methods
    .create("Perk Token", "PERK", "https://test.com/perk.json")
    .accounts({
      signer: authority.publicKey,
      creator: creator.publicKey,
      feeRecipient: feeRecipient.publicKey,
      global,
      mint: perkMint.publicKey,
      tokenProgram: TOKEN_PROGRAM_ID,
    })
    .signers([perkMint])
    .rpc();

  const initialTokenBalance = await getTokenBalance(provider, buyerAta);
  const perk_token_amount = new anchor.BN(
        Math.floor(initialTokenBalance / 2)
  );
  const purchase_token_amount = new anchor.BN( Math.floor(initialTokenBalance / 2) -1 );

  const id = new anchor.BN(3);

  console.log("program ID", program.programId.toBase58());
  console.log("ID buffer:", id.toArrayLike(Buffer, "le", 8));

  try {
    await program.methods
      .escrowBuy(id, purchase_token_amount, perk_token_amount)
      .accounts({
        signer: buyer.publicKey,
        purchaseTokenMint: mint.publicKey,
          perkTokenMint: perkMint.publicKey,
          tokenProgram: TOKEN_PROGRAM_ID,
        })
      .signers([buyer])
      .rpc();
  } catch(error) {
    console.log("Fails as expected");
  }
  });


  it("Allows a SUBSEQUENT buy and charges the percentage fee", async () => {
    const solAmountToBuy = new anchor.BN(1 * LAMPORTS_PER_SOL);
    const secondBuyerAta = getAssociatedTokenAddressSync(
      mint.publicKey,
      secondBuyer.publicKey
    );
    const initialCurveData = await program.account.bondingCurve.fetch(
      bondingCurve
    );

    await program.methods
      .buy(solAmountToBuy, new anchor.BN(0))
      .accounts({
        signer: secondBuyer.publicKey,
        mint: mint.publicKey,
        creator: creator.publicKey,
        feeRecipient: feeRecipient.publicKey,
        tokenProgram: TOKEN_PROGRAM_ID,
      })
      .signers([secondBuyer])
      .rpc();

    const finalCurveData = await program.account.bondingCurve.fetch(
      bondingCurve
    );
    const expectedFee = solAmountToBuy
      .mul(tradingFeeBps)
      .div(new anchor.BN(10000));
    const expectedSolIncrease = solAmountToBuy.sub(expectedFee);
    expect(finalCurveData.realSolReserves.toString()).to.eq(
      initialCurveData.realSolReserves.add(expectedSolIncrease).toString()
    );
  });

  it("Allows a user to sell tokens", async () => {
    const buyerAta = getAssociatedTokenAddressSync(
      mint.publicKey,
      buyer.publicKey
    );
    const initialTokenBalance = await getTokenBalance(provider, buyerAta);
    const tokenAmountToSell = new anchor.BN(
      Math.floor(initialTokenBalance / 2)
    );

    await program.methods
      .sell(tokenAmountToSell, new anchor.BN(0))
      .accounts({
        signer: buyer.publicKey,
        mint: mint.publicKey,
        creator: creator.publicKey,
        feeRecipient: feeRecipient.publicKey,
        tokenProgram: TOKEN_PROGRAM_ID,
      })
      .signers([buyer])
      .rpc();

    const finalTokenBalance = await getTokenBalance(provider, buyerAta);
    expect(finalTokenBalance).to.be.lt(initialTokenBalance);
  });

  it("Allows the creator to withdraw vested tokens", async () => {
    // Set timestamp to 50 days in the past
    const fiftyDaysInSeconds = 50 * 24 * 60 * 60;
    const now = Math.floor(Date.now() / 1000);
    const pastTimestamp = new anchor.BN(now - fiftyDaysInSeconds);

    await program.methods
      .setCreationTimestamp(pastTimestamp)
      .accounts({
        authority: authority.publicKey,
        creator: creator.publicKey,
        mint: mint.publicKey,
      })
      .rpc();

    const creatorDestinationAta = getAssociatedTokenAddressSync(
      mint.publicKey,
      creator.publicKey
    );

    await program.methods
      .withdrawUnlocked()
      .accounts({
        creator: creator.publicKey,
        mint: mint.publicKey,
        tokenProgram: TOKEN_PROGRAM_ID,
      })
      .signers([creator])
      .rpc();

    const creatorTokenBalance = await getTokenBalance(
      provider,
      creatorDestinationAta
    );
    const expectedWithdrawal = creatorVaultAmount.div(new anchor.BN(2)); // ~50%
    expect(creatorTokenBalance).to.be.closeTo(
      expectedWithdrawal.toNumber(),
      expectedWithdrawal.toNumber() * 0.01 // 1% tolerance
    );
  });

  it("Allows the authority to update the global config", async () => {
    const newAuthority = Keypair.generate();
    const newFeeBps = new anchor.BN(200); // 2%

    await program.methods
      .updateGlobalConfig(
        newAuthority.publicKey,
        feeRecipient.publicKey,
        newFeeBps,
        firstBuyFeeSol,
        initialVirtualTokenReserves,
        initialVirtualSolReserves,
        initialRealTokenReserves,
        tokenTotalSupply,
        graduationThreshold
      )
      .accounts({ authority: authority.publicKey })
      .rpc();

    const globalData = await program.account.global.fetch(global);
    expect(globalData.authority.toBase58()).to.eq(
      newAuthority.publicKey.toBase58()
    );
    expect(globalData.tradingFeeBps.toString()).to.eq(newFeeBps.toString());

    await provider.connection.requestAirdrop(
      newAuthority.publicKey,
      1 * LAMPORTS_PER_SOL
    );
    // swith back to original authority
    await program.methods
      .updateGlobalConfig(
        authority.publicKey,
        feeRecipient.publicKey,
        newFeeBps,
        firstBuyFeeSol,
        initialVirtualTokenReserves,
        initialVirtualSolReserves,
        initialRealTokenReserves,
        tokenTotalSupply,
        graduationThreshold
      )
      .accounts({ authority: newAuthority.publicKey })
      .signers([newAuthority])
      .rpc();
  });

  it("Graduates the curve and allows authority withdrawal", async () => {
    // Push the curve over the graduation threshold
    const solAmountToGraduate = new anchor.BN(3 * LAMPORTS_PER_SOL);
    const buyerAta = getAssociatedTokenAddressSync(
      mint.publicKey,
      buyer.publicKey
    );
    await program.methods
      .buy(solAmountToGraduate, new anchor.BN(0))
      .accounts({
        signer: buyer.publicKey,
        mint: mint.publicKey,
        creator: creator.publicKey,
        feeRecipient: feeRecipient.publicKey,
        tokenProgram: TOKEN_PROGRAM_ID,
      })
      .signers([buyer])
      .rpc();

    let curveData = await program.account.bondingCurve.fetch(bondingCurve);
    expect(curveData.complete).to.be.true;

    // Try to buy again, should fail
    try {
      await program.methods
        .buy(new anchor.BN(0.1 * LAMPORTS_PER_SOL), new anchor.BN(0))
        .accounts({
          signer: buyer.publicKey,
          mint: mint.publicKey,
          creator: creator.publicKey,
          feeRecipient: feeRecipient.publicKey,
          tokenProgram: TOKEN_PROGRAM_ID,
        })
        .signers([buyer])
        .rpc();
      expect.fail("Should have failed to buy on a complete curve");
    } catch (e) {
      expect(e.error.errorCode.code).to.equal("BondingCurveComplete");
    }

    // Authority withdraws funds
    const authorityAta = getAssociatedTokenAddressSync(
      mint.publicKey,
      authority.publicKey
    );

    // Create the ATA for the authority if it doesn't exist
    const createAtaIx = createAssociatedTokenAccountInstruction(
      authority.publicKey, // payer
      authorityAta, // ata
      authority.publicKey, // owner
      mint.publicKey // mint
    );

    const setupTx = new anchor.web3.Transaction().add(createAtaIx);

    await provider.sendAndConfirm(setupTx, [], { commitment: "confirmed" });
    const data = await program.account.bondingCurve.fetch(bondingCurve);
    await program.methods
      .withdraw()
      .accounts({
        authority: authority.publicKey,
        mint: mint.publicKey,
        tokenProgram: TOKEN_PROGRAM_ID,
      })
      .signers([authority])
      .rpc();

    const finalCurveTokenBalance = await getTokenBalance(
      provider,
      bondingCurveAta
    );
    expect(finalCurveTokenBalance).to.eq(0);
  });

});