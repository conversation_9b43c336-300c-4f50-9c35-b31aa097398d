const dataContext = require("../db");

// Get all messages for a trade
exports.getMessagesByTrade = async (req, res) => {
  try {
    const { tradeId } = req.params;
    const messages = await dataContext.Message.findAll({
      where: { tradeId },
      order: [["createdAt", "ASC"]],
    });
    res.status(200).json({ status: 200, data: messages });
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error fetching messages" });
  }
};

// Send a new message
exports.sendMessage = async (req, res) => {
  try {
    const { tradeId, senderId, receiverId, message, type } = req.body;
    if (!tradeId || !senderId || !receiverId || !message) {
      return res.status(400).json({ status: 400, message: "Missing required fields" });
    }
    // Enforce only buyer or seller can send messages
    const trade = await dataContext.TokenPurchased.findOne({ where: { id: tradeId } });
    if (!trade) {
      return res.status(404).json({ status: 404, message: "Trade not found" });
    }
    if (trade.userId !== senderId && trade.to !== senderId && trade.from !== senderId) {
      return res.status(403).json({ status: 403, message: "Not authorized for this trade" });
    }
    // Check if receiver allows messages
    const receiver = await dataContext.User.findOne({ where: { id: receiverId } });
    if (!receiver || receiver.allowMessages === false) {
      return res.status(403).json({ status: 403, message: "This user is not accepting messages." });
    }
    const newMessage = await dataContext.Message.create({
      tradeId,
      senderId,
      receiverId,
      message,
      type: type || 'user',
    });
    res.status(201).json({ status: 201, data: newMessage });
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error sending message" });
  }
};

// Get release deadline for a trade
exports.getReleaseDeadline = async (req, res) => {
  try {
    const { tradeId } = req.params;
    const trade = await dataContext.TokenPurchased.findOne({ where: { id: tradeId } });
    if (!trade) {
      return res.status(404).json({ status: 404, message: "Trade not found" });
    }
    // For demo: release is 3 days after createdAt
    const createdAt = trade.createdAt;
    const releaseDeadline = new Date(createdAt);
    releaseDeadline.setDate(releaseDeadline.getDate() + 3);
    res.status(200).json({ status: 200, releaseDeadline });
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error fetching release deadline" });
  }
};

// Initiate or find a chat room for buyer-seller-perk
exports.initiateChatRoom = async (req, res) => {
  try {
    const { buyerId, sellerId, perkId } = req.body;
    if (!buyerId || !sellerId || !perkId) {
      return res.status(400).json({ status: 400, message: "Missing required fields" });
    }
    // Generate a unique chatRoomId (e.g., buyerId-sellerId-perkId)
    const chatRoomId = `${buyerId}-${sellerId}-${perkId}`;
    // Find or create the chat room
    let [chatRoom, created] = await dataContext.ChatRoom.findOrCreate({
      where: { chatRoomId },
      defaults: { buyerId, sellerId, perkId }
    });
    // If new, send a system message and create notifications
    if (created) {
      await dataContext.Message.create({
        chatRoomId,
        senderId: buyerId, // or 0/system
        receiverId: sellerId,
        message: "Buyer started a discussion about this perk.",
        type: "system"
      });
      // Get perk details for notifications
      const perk = await dataContext.Perk.findByPk(perkId);
      const buyer = await dataContext.User.findByPk(buyerId);

      // Create proper notifications for both users
      try {
        const notifications = [
          {
            userId: sellerId,
            type: 'perk_purchased', // This indicates someone is interested in their perk
            title: 'New Chat Started',
            message: `${buyer?.username || 'A user'} started a chat about your perk "${perk?.name || 'Unknown Perk'}"`,
            data: {
              buyerId,
              sellerId,
              perkId,
              chatRoomId,
              perkName: perk?.name
            },
            priority: 'medium',
            actionUrl: `/perks-shop/${perkId}?openChat=1&chatRoomId=${chatRoomId}`
          },
          {
            userId: buyerId,
            type: 'perk_purchased', // This indicates they initiated interest in a perk
            title: 'Chat Initiated',
            message: `You started a chat about "${perk?.name || 'Unknown Perk'}"`,
            data: {
              buyerId,
              sellerId,
              perkId,
              chatRoomId,
              perkName: perk?.name
            },
            priority: 'low',
            actionUrl: `/perks-shop/${perkId}?openChat=1&chatRoomId=${chatRoomId}`
          }
        ];

        await dataContext.Notification.bulkCreate(notifications);
      } catch (error) {
        console.error('Failed to create chat initiation notifications:', error);
      }

      // Keep userLogs for record keeping
      await dataContext.userLogs.create({
        userId: sellerId,
        messageKey: "log.chat.started",
        messageVars: JSON.stringify({ buyerId, perkId }),
        type: "chat_initiate"
      });
      await dataContext.userLogs.create({
        userId: buyerId,
        messageKey: "log.chat.initiated",
        messageVars: JSON.stringify({ sellerId, perkId }),
        type: "chat_initiate"
      });
    }
    return res.status(200).json({ status: 200, chatRoomId, created });
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error initiating chat room" });
  }
};

// Initiate or find a direct chat room between two users (not tied to trade/perk)
exports.initiateDirectChatRoom = async (req, res) => {
  try {
    const { userAId, userBId } = req.body;
    if (!userAId || !userBId) {
      return res.status(400).json({ status: 400, message: "Missing userAId or userBId" });
    }
    // Sort IDs to ensure uniqueness regardless of order
    const [id1, id2] = [userAId, userBId].sort();
    const chatRoomId = `direct-${id1}-${id2}`;
    // Find or create the chat room
    let [chatRoom, created] = await dataContext.ChatRoom.findOrCreate({
      where: { chatRoomId },
      defaults: { chatRoomId, buyerId: id1, sellerId: id2, perkId: 0, type: 'direct' }
    });
    return res.status(200).json({ status: 200, chatRoomId, created });
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error initiating direct chat room", error: error.message });
  }
};

// Get all messages for a chat room by chatRoomId
exports.getMessagesByChatRoom = async (req, res) => {
  try {
    const { chatRoomId } = req.params;
    const messages = await dataContext.Message.findAll({
      where: { chatRoomId },
      order: [["createdAt", "ASC"]],
    });
    res.status(200).json({ status: 200, data: messages });
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error fetching messages by chatRoomId" });
  }
};

// Get chat room info by chatRoomId
exports.getChatRoomInfo = async (req, res) => {
  try {
    const { chatRoomId } = req.params;
    const chatRoom = await dataContext.ChatRoom.findOne({ where: { chatRoomId } })
    if (!chatRoom) {
      return res.status(404).json({ status: 404, message: "Chat room not found" });
    }
    res.status(200).json({ status: 200, data: chatRoom });
  } catch (error) {
    res.status(500).json({ status: 500, message: "Error fetching chat room info" });
  }
}; 