
require('ts-node/register');

const { fn, col, where, Op, literal } = require("sequelize");
const dataContext = require("../db");
const { createSolanaAddress } = require("../utilities/utils");
const { 
  <PERSON>Key, 
  SystemProgram 
} = require('@solana/web3.js');
const { 
  getAssociatedTokenAddressSync,
  ASSOCIATED_TOKEN_PROGRAM_ID,
  TOKEN_PROGRAM_ID 
} = require('@solana/spl-token');
const anchor = require('@coral-xyz/anchor');
const { v4: uuidv4 } = require('uuid');
const { program } = require('../utilities/funhi-program/setup'); // Import the program instance


exports.getPerks = async (req, res, next) => {
  try {
    const { search, sort, order, isVerified } = req.query;
    let { page, pageSize } = req.query;

    let sortQuery;
    if (sort && order) {
      const sorts = sort.split(",");
      const orders = order.split(",");
      sortQuery = sorts
        .map((el, index) => {
          if (orders.length > index) {
            return [el, orders[index] === "desc" ? "desc" : "asc"];
          }
          return null;
        })
        .filter(Boolean);
    }

    const query = {
      where: {},
      order: sortQuery,
      subQuery: false,
      include: [
        {
          model: dataContext.User,
          as: "user",
          attributes: ["id", "username", "email"], // customize if needed
        },
      ],
    };

    if (search) {
      query.where.name = {
        [Op.like]: `%${search}%`,
      };
    }

    if (isVerified === "true") {
      query.where.isVerified = true;
    } else if (isVerified === "false") {
      query.where.isVerified = false;
    }

    if (page !== undefined && pageSize !== undefined) {
      page = parseInt(page);
      pageSize = parseInt(pageSize);
      query.limit = pageSize;
      query.offset = page * pageSize;
    }

    const data = await dataContext.Perk.findAll(query);
    const totalCount = await dataContext.Perk.count({ where: query.where });

    res.status(200).json({
      status: 200,
      message: "Success",
      data: {
        totalCount,
        page,
        pageSize,
        perks: data,
      },
    });
  } catch (err) {
    next(err);
  }
};

exports.addPerk = async (req, res, next) => {
  try {
    const {
      name,
      description,
      address,
      price,
      fulfillmentLink,
      image,
      isLimited,
      category,
      isVerified,
      stockAmount,
      tokenAmount = 1, // Default to 1 token if not specified
      userId,
    } = req.body;

    // Check if perk with same name exists
    const existingPerk = await dataContext.Perk.findOne({
      where: { name },
    });

    if (existingPerk) {
      const error = new Error("Perk with this name already exists.");
      error.statusCode = 401;
      throw error;
    }

    // Generate Solana address
    const { publicKey, secretKey } = createSolanaAddress();

    const categoryRand =
      category ||
      ["Art", "Music", "Photography", "Videography", "Utility"][
        Math.floor(Math.random() * 5)
      ];

    const newPerk = await dataContext.Perk.create({
      name,
      description,
      userId,
      price,
      address: publicKey,
      image:
        image ||
        "https://upload.wikimedia.org/wikipedia/en/b/b9/Solana_logo.png",
      fulfillmentLink,
      isLimited,
      category: categoryRand,
      isVerified,
      stockAmount,
      tokenAmount,
    });

    await dataContext.userLogs.create({
      userId,
      messageKey: "log.perk.created",
      messageVars: JSON.stringify({ name }),
      type: "Created Perk",
    });

    res.status(201).json({
      status: 201,
      message: "Perk created successfully.",
      data: newPerk,
    });
  } catch (err) {
    next(err);
  }
};

exports.getPerk = async (req, res) => {
  const perkId = req.params.id;
  const { userId } = req.body;
  try {
    const perk = await dataContext.Perk.findOne({
      where: { perkId },
      include: [
        {
          model: dataContext.User,
          as: "user",
          attributes: ["id", "username", "email", "name", "privywallet"],
        },
        {
          model: dataContext.Reviews,
          as: "reviews",
          attributes: [
            "id",
            "Perk_ID",
            "User_ID",
            "UserName",
            "Review",
            "updatedAt",
          ],
        },
      ],
    });

    if (!perk) {
      return res.status(200).json({ status: 404, message: "Perk not found" });
    }

    // Convert Sequelize model instance to plain JS object
    const perkData = perk.toJSON();

    // Check if the user already has this perk
    let hasPerk = false;

    if (userId) {
      const userPerk = await dataContext.PerkPurchased.findOne({
        where: { userId, perkId },
      });
      hasPerk = !!userPerk;
    }

    // Attach the flag
    perkData.hasPerk = hasPerk;

    //getting token info linked with perk.
    const tokenData = await dataContext.Token.findOne({
      where: { userId: perkData.userId },
      include: [
        {
          model: dataContext.Comments,
          as: "comments",
          separate: true,
          attributes: [
            "ID",
            "User_ID",
            "UserName",
            "Comment",
            "Stars",
            "createdAt",
            "updatedAt",
            "Avatar",
          ],
          order: [["createdAt", "DESC"]],
        },
      ],
    });

    let tokenIs = [];
    if (!tokenData) {
    } else {
      tokenIs = tokenData.toJSON();
    }
    perkData.token = tokenIs;

    // Respond with modified object
    res.status(200).json({
      status: 200,
      message: "Get Perk successfully.",
      data: perkData,
    });
  } catch (error) {
    console.error("Error fetching perk:", error);
    res
      .status(200)
      .json({
        status: 500,
        message: "An unexpected error occurred. Please try again later.",
      });
  }
};

// exports.purchasePerk = async (req, res, next) => {
//   try {
//     const { userId, perkId, amount = 1, price, extra, signature } = req.body;

//     // Check if the perk exists
//     const perk = await dataContext.Perk.findOne({ where: { perkId } });
//     if (!perk) {
//       const error = new Error("Perk not found.");
//       error.statusCode = 404;
//       throw error;
//     }

//     // Check if the user exists
//     const user = await dataContext.User.findOne({ where: { id: userId } });
//     if (!user) {
//       const error = new Error("User not found.");
//       error.statusCode = 404;
//       throw error;
//     }

//     // Check if user already purchased this perk
//     let existingPurchase = await dataContext.PerkPurchased.findOne({
//       where: { userId, perkId },
//     });

//     if (existingPurchase) {
//       // Update existing purchase
//       existingPurchase.amount += amount;
//       existingPurchase.price = price || existingPurchase.price;
//       existingPurchase.extra = extra || existingPurchase.extra;
//       await existingPurchase.save();

//       await dataContext.userLogs.create({
//         userId,
//         message: `Purchased Perk updated.`,
//         messageDetail: `User purchased Perk updated.`,
//         type: "Buy Perk",
//       });

//       return res.status(200).json({
//         status: 200,
//         message: "Perk purchase updated successfully",
//         data: existingPurchase,
//       });
//     }

//     // Get wallet addresses
//     const buyerWallet = req.body.buyerWallet;
//     const sellerWallet = sellerToken?.creatorWallet; // FIX: use sellerToken.creatorWallet
//     if (!buyerWallet || !sellerWallet) {
//       return res.status(200).json({ status: 404, message: 'Buyer or seller wallet not found' });
//     }

//     // Prepare contract call parameters
//     const solAmount = new anchor.BN(Math.floor(price * 1_000_000_000)); // Use anchor.BN
//     const minTokenOutput = new anchor.BN(1); // Use anchor.BN
//     const mint = new PublicKey(sellerToken.tokenAddress);
//     const creator = new PublicKey(sellerWallet);
//     const buyerPubKey = new PublicKey(buyerWallet);

//     // Call the contract utility to lock funds in escrow
//     let escrowTxId = null;
//     try {
//       const txIx = await buy(solAmount, minTokenOutput, mint, creator, buyerPubKey);
//       // You would send and confirm the transaction here, and get the transaction signature
//       // For now, store the instruction as a placeholder
//       escrowTxId = txIx ? 'INSTRUCTION_CREATED' : null;
//     } catch (err) {
//       console.error('Error calling contract utility:', err);
//       return res.status(500).json({ status: 500, message: 'Failed to lock funds on-chain.' });
//     }

//     // Create TokenPurchased (trade/escrow) record
//     const tokenPurchased = await dataContext.TokenPurchased.create({
//       userId,
//       tokenId: sellerToken.tokenId,
//       perkId,
//       amount,
//       price,
//       escrowTxId,
//       fulfillmentStatus: 'pending',
//       disputeStatus: 'none',
//       status: 'pending',
//       extra,
//       from: buyerWallet,
//       to: sellerWallet,
//     });

//     // Create system message for trade (escrow created)
//     await dataContext.Message.create({
//       tradeId: tokenPurchased.id,
//       senderId: userId,
//       receiverId: perk.userId, // seller
//       message: 'Escrow created. Funds are locked on-chain.',
//       type: 'system',
//     });

//     // Log user action
//     await dataContext.userLogs.create({
//       userId,
//       messageKey: "log.token.purchased",
//       messageVars: JSON.stringify({ amount: amount || 1, tokenName: perk.name }),
//       type: 'Buy Perk',
//     });

//     // Return tradeId to frontend
//     res.status(200).json({
//       status: 200,
//       message: 'Perk purchased and escrow created successfully',
//       data: {
//         tradeId: tokenPurchased.id,
//         escrowTxId,
//         tokenPurchased,
//         sellerUserId: perk.userId // <-- add seller's userId for chat
//       },
//     });
//   } catch (error) {
//     console.error('Error purchasing perk:', error);
//     res.status(200).json({ status: 500, message: 'An unexpected error occurred. Please try again later.' });
//   }
// };

exports.purchasePerk = async (req, res, next) => {
  try {
    const { userId, perkId, amount = 1, price, buyerWallet } = req.body;
    
    // Validate inputs
    if (!buyerWallet) {
      return res.status(400).json({ status: 400, message: 'Buyer wallet required' });
    }

    // Get perk and seller token
    const perk = await dataContext.Perk.findOne({ where: { perkId } });
    if (!perk) {
      return res.status(404).json({ status: 404, message: 'Perk not found' });
    }

    const sellerToken = await dataContext.Token.findOne({ 
      where: { userId: perk.userId } 
    });
    if (!sellerToken) {
      return res.status(404).json({ status: 404, message: 'Seller token not found' });
    }

    const sellerWallet = sellerToken.creatorWallet;

    // Debug wallet addresses
    console.log('🔍 [Purchase] Wallet addresses:');
    console.log('  buyerWallet:', buyerWallet);
    console.log('  sellerWallet:', sellerWallet);
    console.log('  sellerToken.creatorWallet:', sellerToken.creatorWallet);
    console.log('  perk.userId:', perk.userId);
    console.log('  userId (buyer):', userId);

    // Check if user is trying to buy their own perk
    if (userId === perk.userId) {
      return res.status(400).json({
        status: 400,
        message: 'You cannot purchase your own perk'
      });
    }

    // Generate unique escrow ID (use timestamp + random for u64)
    const escrowId = Date.now() + Math.floor(Math.random() * 1000);
    const purchaseTokenMint = new PublicKey('So11111111111111111111111111111111111111112'); // SOL mint
    const perkTokenMint = new PublicKey(sellerToken.tokenAddress);

    // Convert price to lamports and use perk's tokenAmount (not the purchase amount)
    const solAmount = new anchor.BN(Math.floor(price * anchor.web3.LAMPORTS_PER_SOL));

    // Get token decimals from the blockchain
    let tokenDecimals = 6; // Default fallback
    try {
      const connection = new anchor.web3.Connection(process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com');
      const mintInfo = await connection.getParsedAccountInfo(perkTokenMint);
      if (mintInfo.value && mintInfo.value.data && 'parsed' in mintInfo.value.data) {
        tokenDecimals = mintInfo.value.data.parsed.info.decimals;
        console.log(`🔍 [Purchase] Token decimals: ${tokenDecimals}`);
      }
    } catch (error) {
      console.warn('Failed to fetch token decimals, using default:', error.message);
    }

    // Convert perk token amount to smallest unit (accounting for decimals)
    const perkTokenAmountRaw = perk.tokenAmount || 1;
    const perkTokenAmount = new anchor.BN(perkTokenAmountRaw * Math.pow(10, tokenDecimals));

    console.log(`🔍 [Purchase] Perk token amount: ${perkTokenAmountRaw} tokens = ${perkTokenAmount.toString()} smallest units (${tokenDecimals} decimals)`);

    // Calculate required PDAs
    const escrowIdBN = new anchor.BN(escrowId);
    const [purchasePda] = PublicKey.findProgramAddressSync(
      [Buffer.from("purchase"), escrowIdBN.toArrayLike(Buffer, "le", 8)],
      program.programId
    );

    const [vaultPda] = PublicKey.findProgramAddressSync(
      [purchasePda.toBuffer(), TOKEN_PROGRAM_ID.toBuffer(), purchaseTokenMint.toBuffer()],
      ASSOCIATED_TOKEN_PROGRAM_ID
    );

    // Prepare escrow transaction data for frontend signing
    console.log('Preparing escrow transaction data:', {
      escrowId,
      solAmount: solAmount.toString(),
      perkTokenAmount: perkTokenAmount.toString(),
      buyerWallet,
      purchasePda: purchasePda.toString(),
      vaultPda: vaultPda.toString()
    });

    // Return transaction data for frontend to sign and execute
    const escrowTxData = {
      escrowId,
      solAmount: solAmount.toString(),
      perkTokenAmount: perkTokenAmount.toString(),
      purchasePda: purchasePda.toString(),
      vaultPda: vaultPda.toString(),
      purchaseTokenMint: purchaseTokenMint.toString(),
      perkTokenMint: perkTokenMint.toString(),
      buyerWallet,
      sellerWallet
    };

    // Create trade record (transaction will be updated after frontend signs)
    const tokenPurchased = await dataContext.TokenPurchased.create({
      userId,
      tokenId: sellerToken.tokenId,
      perkId,
      amount,
      price,
      escrowTxId: null, // Will be updated after frontend signs
      escrowId,
      fulfillmentStatus: 'pending',
      status: 'pending_signature', // Status indicates waiting for signature
      from: buyerWallet,
      to: sellerWallet,
      perkTokenMint: sellerToken.tokenAddress
    });

    // Create or find existing chat room using existing message controller logic
    const chatRoomId = `${userId}-${perk.userId}-${perkId}`; // Format: buyerId-sellerId-perkId
    let [chatRoom, created] = await dataContext.ChatRoom.findOrCreate({
      where: { chatRoomId },
      defaults: {
        buyerId: userId,
        sellerId: perk.userId,
        perkId: parseInt(perkId)
      }
    });

    // Chat room already exists, no need to update since we don't store trade info in ChatRoom

    // Create system message
    await dataContext.Message.create({
      chatRoomId: chatRoom.chatRoomId, // Use the chatRoomId string
      senderId: userId,
      receiverId: perk.userId,
      message: 'Escrow created. Funds locked on-chain.',
      type: 'system'
    });

    // Create notifications for both buyer and seller
    try {
      const notificationCount = await dataContext.Notification.createPerkPurchaseNotifications(
        userId, // buyerId
        perk.userId, // sellerId
        perk.name, // perkName
        price, // price
        tokenPurchased.id, // tradeId
        chatRoom.chatRoomId, // chatRoomId
        perkId // perkId
      );
      console.log(`Created ${notificationCount} perk purchase notifications`);
    } catch (error) {
      console.error('Failed to create perk purchase notifications:', error);
      // Don't fail the purchase if notifications fail
    }

    // TODO: Emit real-time notification via socket.io
    // This will be implemented when we add socket.io to the perk controller

    res.status(200).json({
      status: 200,
      message: 'Escrow prepared successfully - please sign transaction',
      data: {
        tradeId: tokenPurchased.id,
        chatRoomId: chatRoom.chatRoomId, // Use the chatRoomId string, not the database ID
        sellerUserId: perk.userId,
        escrowTxData // Transaction data for frontend to sign
      },
    });
  } catch (error) {
    console.error('Purchase error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

// Confirm escrow transaction after frontend signing
exports.confirmEscrow = async (req, res, next) => {
  try {
    const { tradeId, txId } = req.body;

    if (!tradeId || !txId) {
      return res.status(400).json({ status: 400, message: 'Trade ID and transaction ID required' });
    }

    // Update trade record with transaction ID and status
    const trade = await dataContext.TokenPurchased.findByPk(tradeId);
    if (!trade) {
      return res.status(404).json({ status: 404, message: 'Trade not found' });
    }

    await trade.update({
      escrowTxId: txId,
      status: 'escrowed'
    });

    res.status(200).json({
      status: 200,
      message: 'Escrow confirmed successfully',
      data: { tradeId, txId }
    });
  } catch (error) {
    console.error('Confirm escrow error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

// Initiate pre-purchase chat
exports.initiatePerkChat = async (req, res, next) => {
  try {
    const { userId, perkId } = req.body;

    if (!userId || !perkId) {
      return res.status(400).json({ status: 400, message: 'User ID and Perk ID required' });
    }

    // Get perk details
    const perk = await dataContext.Perk.findOne({
      where: { perkId },
      include: [{ model: dataContext.User, as: 'user' }]
    });

    if (!perk) {
      return res.status(404).json({ status: 404, message: 'Perk not found' });
    }

    // Prevent self-chat
    if (perk.userId === userId) {
      return res.status(400).json({ status: 400, message: 'Cannot chat with yourself' });
    }

    // Create or find existing chat room
    const chatRoomId = `${userId}-${perk.userId}-${perkId}`;
    let chatRoom = await dataContext.ChatRoom.findOne({
      where: { chatRoomId }
    });

    if (!chatRoom) {
      chatRoom = await dataContext.ChatRoom.create({
        chatRoomId,
        buyerId: userId,
        sellerId: perk.userId,
        perkId: parseInt(perkId)
      });

      // Create welcome system message
      await dataContext.Message.create({
        chatRoomId: chatRoom.chatRoomId,
        senderId: userId,
        receiverId: perk.userId,
        message: `Chat initiated for perk: ${perk.name}`,
        type: 'system'
      });
    }

    res.status(200).json({
      status: 200,
      message: 'Chat room ready',
      data: {
        chatRoomId: chatRoom.chatRoomId,
        perkName: perk.name,
        sellerName: perk.user.username,
        sellerId: perk.userId
      }
    });
  } catch (error) {
    console.error('Initiate chat error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

exports.releasePerk = async (req, res, next) => {
  try {
    const { tradeId, sellerWallet } = req.body;
    const trade = await dataContext.TokenPurchased.findByPk(tradeId);
    
    if (!trade || trade.status !== 'escrowed') {
      return res.status(400).json({ status: 400, message: 'Invalid trade state' });
    }

    // Frontend handles the smart contract transaction
    // Backend only updates database status
    console.log('Processing escrow release for trade:', trade.id);

    try {
      // Update trade status
      await trade.update({
        status: 'completed',
        fulfillmentStatus: 'fulfilled'
      });

      // Find chat room using the standard pattern: buyerId-sellerId-perkId
      // We need to get the perk to find the seller
      const perk = await dataContext.Perk.findByPk(trade.perkId);
      if (!perk) {
        throw new Error('Perk not found for trade');
      }

      const chatRoomId = `${trade.userId}-${perk.userId}-${trade.perkId}`;
      const chatRoom = await dataContext.ChatRoom.findOne({
        where: { chatRoomId }
      });

      if (chatRoom) {
        await dataContext.Message.create({
          chatRoomId: chatRoom.chatRoomId, // Use chatRoomId string, not database id
          senderId: trade.userId,
          receiverId: chatRoom.sellerId,
          message: 'Funds released to seller',
          type: 'system'
        });
      }

      // Create escrow release notifications only if chatRoom exists
      if (chatRoom) {
        try {
          const notificationCount = await dataContext.Notification.createEscrowReleaseNotifications(
            trade.userId, // buyerId
            chatRoom.sellerId, // sellerId
            perk?.name || 'Unknown Perk', // perkName
            trade.id, // tradeId
            trade.perkId, // perkId
            chatRoom.chatRoomId // chatRoomId
          );
          console.log(`Created ${notificationCount} escrow release notifications`);
        } catch (error) {
          console.error('Failed to create escrow release notifications:', error);
        }
      } else {
        console.warn('No chat room found for trade:', trade.id);
      }

      res.status(200).json({
        status: 200,
        message: 'Funds released successfully'
      });
    } catch (error) {
      console.error('Release error:', error);
      res.status(500).json({ status: 500, message: 'Release failed' });
    }
  } catch (error) {
    next(error);
  }
};

exports.refundPerk = async (req, res, next) => {
  try {
    const { tradeId, buyerWallet } = req.body;
    const trade = await dataContext.TokenPurchased.findByPk(tradeId);
    
    if (!trade || trade.status !== 'escrowed') {
      return res.status(400).json({ status: 400, message: 'Invalid trade state' });
    }

    // Frontend handles the smart contract transaction
    // Backend only updates database status
    console.log('Processing escrow refund for trade:', trade.id);

    try {

      // Update trade status
      await trade.update({
        status: 'refunded',
        fulfillmentStatus: 'cancelled'
      });

      // Find chat room using the standard pattern: buyerId-sellerId-perkId
      // We need to get the perk to find the seller
      const perk = await dataContext.Perk.findByPk(trade.perkId);
      if (!perk) {
        throw new Error('Perk not found for trade');
      }

      const chatRoomId = `${trade.userId}-${perk.userId}-${trade.perkId}`;
      const chatRoom = await dataContext.ChatRoom.findOne({
        where: { chatRoomId }
      });

      if (chatRoom) {
        await dataContext.Message.create({
          chatRoomId: chatRoom.chatRoomId, // Use chatRoomId string, not database id
          senderId: trade.userId,
          receiverId: chatRoom.sellerId,
          message: 'Escrow refunded to buyer',
          type: 'system'
        });
      }

      // Create trade refund notifications only if chatRoom exists
      if (chatRoom) {
        try {
          const notificationCount = await dataContext.Notification.createTradeRefundNotifications(
            trade.userId, // buyerId
            chatRoom.sellerId, // sellerId
            perk?.name || 'Unknown Perk', // perkName
            trade.id, // tradeId
            trade.perkId, // perkId
            chatRoom.chatRoomId // chatRoomId
          );
          console.log(`Created ${notificationCount} trade refund notifications`);
        } catch (error) {
          console.error('Failed to create trade refund notifications:', error);
        }
      } else {
        console.warn('No chat room found for trade:', trade.id);
      }

      res.status(200).json({
        status: 200,
        message: 'Refund processed successfully'
      });
    } catch (error) {
      console.error('Refund error:', error);
      res.status(500).json({ status: 500, message: 'Refund failed' });
    }
  } catch (error) {
    next(error);
  }
};

exports.postReviews = async (req, res, next) => {
  try {
    const { userId, perkId, username, review, avatar } = req.body;

    // Create review with default Stars = 1
    await dataContext.Reviews.create({
      User_ID: userId,
      Perk_ID: perkId,
      UserName: username,
      Review: review,
      Avatar: avatar || null,
      Stars: 1,
    });

    // Fetch latest 10 reviews for the perk
    const allComments = await dataContext.Reviews.findAll({
      where: { Perk_ID: perkId },
      attributes: [
        "ID",
        "User_ID",
        "UserName",
        "Review",
        "Stars",
        "createdAt",
        "updatedAt",
        "Avatar",
      ],
      order: [["createdAt", "DESC"]],
      limit: 10,
    });

    res.status(201).json({
      status: 201,
      message: "Review posted successfully.",
      data: allComments,
    });
  } catch (err) {
    next(err);
  }
};

// Report trade for dispute resolution
exports.reportTrade = async (req, res) => {
  try {
    const { tradeId, reportReason, reportDetails } = req.body;

    if (!tradeId || !reportReason) {
      return res.status(400).json({ status: 400, message: 'Trade ID and report reason required' });
    }

    // Find the trade
    const trade = await dataContext.TokenPurchased.findByPk(tradeId);
    if (!trade) {
      return res.status(404).json({ status: 404, message: 'Trade not found' });
    }

    // Get perk details for notification
    const perk = await dataContext.Perk.findByPk(trade.perkId);

    // Get chat room for the trade
    const chatRoom = await dataContext.ChatRoom.findOne({
      where: { tradeId }
    });

    // Create trade report notification for the seller
    try {
      const tradeUrl = chatRoom
        ? `/perks-shop/${trade.perkId}?tradeId=${tradeId}&openChat=1&chatRoomId=${chatRoom.chatRoomId}`
        : `/perks-shop/${trade.perkId}?tradeId=${tradeId}`;

      await dataContext.Notification.create({
        userId: trade.to, // Seller being reported
        type: 'trade_reported',
        title: 'Trade Reported',
        message: `Your trade for "${perk?.name || 'Unknown Perk'}" has been reported by the buyer. Reason: ${reportReason}. Click to view chat.`,
        data: {
          tradeId,
          perkId: trade.perkId,
          reportReason,
          reportDetails,
          reporterId: trade.userId,
          buyerId: trade.userId,
          sellerId: trade.to,
          chatRoomId: chatRoom?.chatRoomId
        },
        priority: 'high',
        actionUrl: tradeUrl
      });
    } catch (error) {
      console.error('Failed to create trade report notification:', error);
    }

    // Also create userLog for record keeping
    await dataContext.userLogs.create({
      userId: trade.userId, // Reporter (buyer)
      message: `Trade reported: ${reportReason}`,
      messageDetail: reportDetails || 'No additional details provided',
      messageKey: 'trade_reported',
      messageVars: JSON.stringify({
        tradeId,
        reportReason,
        reportDetails,
        reportedSellerId: trade.to
      }),
      type: 'report'
    });

    // Update trade status to disputed
    await trade.update({ status: 'disputed' });

    // Create system message in chat (reuse existing chatRoom variable)
    if (chatRoom) {
      await dataContext.Message.create({
        chatRoomId: chatRoom.chatRoomId,
        senderId: trade.userId,
        receiverId: trade.to,
        message: `Trade has been reported for: ${reportReason}. Admin review initiated.`,
        type: 'system'
      });
    }

    res.status(200).json({
      status: 200,
      message: 'Trade reported successfully'
    });
  } catch (error) {
    console.error('Report trade error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};

/**
 * Get trade details for dispute/escrow management
 */
exports.getTradeDetails = async (req, res, next) => {
  try {
    const { tradeId } = req.params;
    const userId = req.user.id;

    const trade = await dataContext.TokenPurchased.findByPk(tradeId, {
      include: [
        { model: dataContext.User, as: 'user', attributes: ['id', 'username', 'privywallet'] },
        {
          model: dataContext.Perk,
          as: 'perkDetails',
          include: [{ model: dataContext.User, as: 'user', attributes: ['id', 'username', 'privywallet'] }]
        },
        { model: dataContext.Dispute, as: 'dispute' }
      ]
    });

    if (!trade) {
      return res.status(404).json({ status: 404, message: 'Trade not found' });
    }

    // Check if user has access to this trade
    const buyerId = trade.userId;
    const sellerId = trade.perkDetails.userId;

    if (userId !== buyerId && userId !== sellerId) {
      return res.status(403).json({ status: 403, message: 'Access denied' });
    }

    res.status(200).json({
      status: 200,
      message: 'Trade details retrieved successfully',
      data: trade
    });

  } catch (error) {
    console.error('Get trade details error:', error);
    res.status(500).json({ status: 500, message: 'Internal server error' });
  }
};
