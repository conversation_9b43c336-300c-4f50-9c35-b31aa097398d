import { useState, useCallback, useRef } from 'react';
import {
  createEscrowBuyTransaction,
  createEscrowSellTransaction,
  createEscrowRefundTransaction,
  initiateEscrowDispute,
  EscrowTxData,
  EscrowOperationType,
  EscrowOperationStatus,
  EscrowOperationState,
  EscrowOperationsState,
  EscrowError,
  TransactionError,
  InsufficientFundsError,
  DuplicateTransactionError,
  BlockchainError
} from '@/utils/escrow';
import { validateEscrowOperation, ValidationResult } from '@/utils/preTransactionValidation';
import { detectAndEnhanceError, DetectedError } from '@/utils/errorDetection';
import { showErrorToast, showSuccessToast, showInfoToast } from '@/utils/errorHandling';

// Initial state for each operation
const createInitialOperationState = (): EscrowOperationState => ({
  status: 'idle',
  error: null,
  txId: null,
  timestamp: 0
});

// Initial state for all operations
const createInitialState = (): EscrowOperationsState => ({
  buy: createInitialOperationState(),
  sell: createInitialOperationState(),
  refund: createInitialOperationState(),
  dispute: createInitialOperationState()
});

export interface UseEscrowOperationsProps {
  onOperationStart?: (operation: EscrowOperationType) => void;
  onOperationSuccess?: (operation: EscrowOperationType, result: any) => void;
  onOperationError?: (operation: EscrowOperationType, error: DetectedError) => void;
  onOperationComplete?: (operation: EscrowOperationType) => void;
  onValidationError?: (operation: EscrowOperationType, validation: ValidationResult) => void;
  showToasts?: boolean;
  showModals?: boolean;
  showCongratulations?: boolean;
  onShowCongratulations?: (operation: EscrowOperationType, txId: string, amount?: string, additionalData?: any) => void;
}

export interface UseEscrowOperationsReturn {
  // State
  operations: EscrowOperationsState;
  isAnyOperationPending: boolean;
  pendingOperations: EscrowOperationType[];
  getOperationStatus: (operation: EscrowOperationType) => EscrowOperationStatus;
  getOperationError: (operation: EscrowOperationType) => Error | null;
  getOperationTxId: (operation: EscrowOperationType) => string | null;

  // Loading state coordination
  canExecuteOperation: (operation: EscrowOperationType) => boolean;
  getBlockingOperations: (operation: EscrowOperationType) => EscrowOperationType[];
  isOperationBlocked: (operation: EscrowOperationType) => boolean;

  // Validation
  validateOperation: (operation: EscrowOperationType, data: any, wallet: any) => Promise<ValidationResult>;

  // Operations with enhanced error handling
  executeBuy: (escrowTxData: EscrowTxData, wallet: any, skipValidation?: boolean) => Promise<string>;
  executeSell: (
    escrowId: string,
    purchaseTokenMint: string,
    perkTokenMint: string,
    sellerWallet: string,
    buyerWallet: string,
    wallet: any,
    skipValidation?: boolean
  ) => Promise<string>;
  executeRefund: (
    escrowId: string,
    purchaseTokenMint: string,
    buyerWallet: string,
    wallet: any,
    skipValidation?: boolean
  ) => Promise<string>;
  executeDispute: (
    tradeId: number,
    reason: string,
    initiatorRole: 'buyer' | 'seller',
    skipValidation?: boolean
  ) => Promise<{ disputeId: number; status: string }>;

  // Utilities
  clearOperation: (operation: EscrowOperationType) => void;
  clearAllOperations: () => void;
  retryOperation: (operation: EscrowOperationType) => Promise<void>;
}

export const useEscrowOperations = ({
  onOperationStart,
  onOperationSuccess,
  onOperationError,
  onOperationComplete,
  onValidationError,
  showToasts = true,
  showModals = true,
  showCongratulations = true,
  onShowCongratulations
}: UseEscrowOperationsProps = {}): UseEscrowOperationsReturn => {
  const [operations, setOperations] = useState<EscrowOperationsState>(createInitialState());
  
  // Store the last operation parameters for retry functionality
  const lastOperationParams = useRef<{
    [K in EscrowOperationType]?: any[]
  }>({});

  // Helper function to update operation state
  const updateOperationState = useCallback((
    operation: EscrowOperationType,
    updates: Partial<EscrowOperationState>
  ) => {
    setOperations(prev => ({
      ...prev,
      [operation]: {
        ...prev[operation],
        ...updates,
        timestamp: Date.now()
      }
    }));
  }, []);

  // Helper function to handle operation start
  const handleOperationStart = useCallback((operation: EscrowOperationType) => {
    updateOperationState(operation, {
      status: 'pending',
      error: null,
      txId: null
    });
    
    onOperationStart?.(operation);
    
    if (showToasts) {
      const messages = {
        buy: 'Creating escrow transaction...',
        sell: 'Releasing escrow funds...',
        refund: 'Processing refund...',
        dispute: 'Initiating dispute...'
      };
      showInfoToast(messages[operation]);
    }
  }, [updateOperationState, onOperationStart, showToasts]);

  // Helper function to handle operation success
  const handleOperationSuccess = useCallback((
    operation: EscrowOperationType,
    result: any,
    txId?: string,
    additionalData?: any
  ) => {
    updateOperationState(operation, {
      status: 'success',
      error: null,
      txId: txId || null
    });

    onOperationSuccess?.(operation, result);
    onOperationComplete?.(operation);

    // Show congratulations modal for successful operations
    if (showCongratulations && txId && onShowCongratulations) {
      onShowCongratulations(operation, txId, additionalData?.amount, additionalData);
    }

    if (showToasts && !showCongratulations) {
      const messages = {
        buy: 'Escrow transaction created successfully!',
        sell: 'Escrow funds released successfully!',
        refund: 'Refund processed successfully!',
        dispute: 'Dispute initiated successfully!'
      };
      showSuccessToast(messages[operation]);
    }
  }, [updateOperationState, onOperationSuccess, onOperationComplete, showCongratulations, onShowCongratulations, showToasts]);

  // Helper function to handle operation error with enhanced detection
  const handleOperationError = useCallback((
    operation: EscrowOperationType,
    error: Error,
    context?: any
  ) => {
    // Detect and enhance the error
    const detectedError = detectAndEnhanceError(error, operation, context);

    updateOperationState(operation, {
      status: 'error',
      error,
      txId: null
    });

    onOperationError?.(operation, detectedError);
    onOperationComplete?.(operation);

    if (showToasts) {
      showErrorToast(detectedError.userFriendlyMessage);
    }
  }, [updateOperationState, onOperationError, onOperationComplete, showToasts]);

  // Operation conflict rules - which operations block others
  const operationConflicts: Record<EscrowOperationType, EscrowOperationType[]> = {
    buy: ['sell', 'refund'], // Can't sell or refund while buying
    sell: ['buy', 'refund'], // Can't buy or refund while selling
    refund: ['buy', 'sell'], // Can't buy or sell while refunding
    dispute: [] // Disputes can run alongside other operations
  };

  // Check if an operation can be executed
  const canExecuteOperation = useCallback((operation: EscrowOperationType): boolean => {
    // Check if the operation itself is pending
    if (operations[operation].status === 'pending') {
      return false;
    }

    // Check if any conflicting operations are pending
    const conflicts = operationConflicts[operation];
    return !conflicts.some(conflictOp => operations[conflictOp].status === 'pending');
  }, [operations]);

  // Get operations that are blocking a specific operation
  const getBlockingOperations = useCallback((operation: EscrowOperationType): EscrowOperationType[] => {
    const conflicts = operationConflicts[operation];
    return conflicts.filter(conflictOp => operations[conflictOp].status === 'pending');
  }, [operations]);

  // Check if an operation is blocked
  const isOperationBlocked = useCallback((operation: EscrowOperationType): boolean => {
    return getBlockingOperations(operation).length > 0;
  }, [getBlockingOperations]);

  // Validation function
  const validateOperation = useCallback(async (
    operation: EscrowOperationType,
    data: any,
    wallet: any
  ): Promise<ValidationResult> => {
    try {
      const validation = await validateEscrowOperation(operation, wallet, data);

      if (!validation.isValid && onValidationError) {
        onValidationError(operation, validation);
      }

      return validation;
    } catch (error: any) {
      console.error('Validation failed:', error);
      return {
        isValid: false,
        errors: [{
          category: 'VALIDATION_ERROR',
          message: 'Pre-transaction validation failed',
          details: error.message,
          code: 'VALIDATION_FAILED'
        }],
        warnings: []
      };
    }
  }, [onValidationError]);

  // Generic operation executor with enhanced coordination and validation
  const executeOperation = useCallback(async <T>(
    operation: EscrowOperationType,
    operationFn: () => Promise<T>,
    params: any[],
    skipValidation: boolean = false,
    validationData?: any,
    wallet?: any
  ): Promise<T> => {
    // Check if operation can be executed
    if (!canExecuteOperation(operation)) {
      const blockingOps = getBlockingOperations(operation);
      if (blockingOps.length > 0) {
        throw new DuplicateTransactionError(
          `Cannot execute ${operation} operation. The following operations are in progress: ${blockingOps.join(', ')}`
        );
      } else {
        throw new DuplicateTransactionError(`${operation} operation already in progress`);
      }
    }

    // Pre-transaction validation (unless skipped)
    if (!skipValidation && validationData && wallet) {
      try {
        const validation = await validateOperation(operation, validationData, wallet);
        if (!validation.isValid) {
          // Only fail on critical errors, not warnings or minor issues
          const criticalErrors = validation.errors.filter(error => {
            console.log(`Validation error: ${error.category}, shortfall: ${error.additionalInfo?.shortfall}`);
            return error.category === 'WALLET_CONNECTION' ||
                   error.category === 'VALIDATION_ERROR' ||
                   (error.category === 'INSUFFICIENT_FUNDS' && error.additionalInfo?.shortfall > 0.1); // Only fail if shortfall is significant
          });

          if (criticalErrors.length > 0) {
            const firstError = criticalErrors[0];
            throw new EscrowError(
              firstError.message,
              firstError.code || 'VALIDATION_FAILED'
            );
          }
        }
      } catch (validationError) {
        // If validation itself fails, log it but don't block the transaction
        console.warn('Pre-transaction validation failed:', validationError);
      }
    }

    // Store parameters for retry
    lastOperationParams.current[operation] = params;

    try {
      handleOperationStart(operation);
      const result = await operationFn();

      // Extract txId if result is a string (transaction ID)
      const txId = typeof result === 'string' ? result : undefined;

      // Prepare additional data for congratulations modal
      const additionalData = {
        amount: validationData?.escrowTxData?.solAmount ? `${validationData.escrowTxData.solAmount} SOL` : undefined,
        escrowId: validationData?.escrowTxData?.escrowId || validationData?.escrowId,
        chatRoomId: validationData?.chatRoomId
      };

      handleOperationSuccess(operation, result, txId, additionalData);

      return result;
    } catch (error) {
      console.error(`${operation} operation failed:`, error);

      // Create context for error detection
      const context = {
        walletAddress: wallet?.address,
        operation
      };

      // Handle the error with enhanced detection
      handleOperationError(operation, error as Error, context);
      throw error;
    }
  }, [operations, canExecuteOperation, getBlockingOperations, validateOperation, handleOperationStart, handleOperationSuccess, handleOperationError]);

  // Specific operation executors with validation
  const executeBuy = useCallback(async (
    escrowTxData: EscrowTxData,
    wallet: any,
    skipValidation: boolean = false
  ): Promise<string> => {
    const validationData = { escrowTxData };
    return executeOperation(
      'buy',
      () => createEscrowBuyTransaction(escrowTxData, wallet),
      [escrowTxData, wallet],
      skipValidation,
      validationData,
      wallet
    );
  }, [executeOperation]);

  const executeSell = useCallback(async (
    escrowId: string,
    purchaseTokenMint: string,
    perkTokenMint: string,
    sellerWallet: string,
    buyerWallet: string,
    wallet: any,
    skipValidation: boolean = false,
    tradeId?: string | number
  ): Promise<string> => {
    const validationData = { escrowId, purchaseTokenMint, perkTokenMint, sellerWallet, buyerWallet };
    return executeOperation(
      'sell',
      () => createEscrowSellTransaction(escrowId, purchaseTokenMint, perkTokenMint, sellerWallet, buyerWallet, wallet, tradeId),
      [escrowId, purchaseTokenMint, perkTokenMint, sellerWallet, buyerWallet, wallet, tradeId],
      skipValidation,
      validationData,
      wallet
    );
  }, [executeOperation]);

  const executeRefund = useCallback(async (
    escrowId: string,
    purchaseTokenMint: string,
    buyerWallet: string,
    wallet: any,
    skipValidation: boolean = false
  ): Promise<string> => {
    const validationData = { escrowId, purchaseTokenMint, buyerWallet };
    return executeOperation(
      'refund',
      () => createEscrowRefundTransaction(escrowId, purchaseTokenMint, buyerWallet, wallet),
      [escrowId, purchaseTokenMint, buyerWallet, wallet],
      skipValidation,
      validationData,
      wallet
    );
  }, [executeOperation]);

  const executeDispute = useCallback(async (
    tradeId: number,
    reason: string,
    initiatorRole: 'buyer' | 'seller',
    skipValidation: boolean = false
  ): Promise<{ disputeId: number; status: string }> => {
    const validationData = { tradeId, reason, initiatorRole };
    return executeOperation(
      'dispute',
      () => initiateEscrowDispute(tradeId, reason, initiatorRole),
      [tradeId, reason, initiatorRole],
      skipValidation,
      validationData,
      null // Dispute doesn't require wallet for validation
    );
  }, [executeOperation]);

  // Utility functions
  const clearOperation = useCallback((operation: EscrowOperationType) => {
    updateOperationState(operation, createInitialOperationState());
    delete lastOperationParams.current[operation];
  }, [updateOperationState]);

  const clearAllOperations = useCallback(() => {
    setOperations(createInitialState());
    lastOperationParams.current = {};
  }, []);

  const retryOperation = useCallback(async (operation: EscrowOperationType): Promise<void> => {
    const params = lastOperationParams.current[operation];
    if (!params) {
      throw new Error(`No parameters stored for ${operation} operation`);
    }
    
    switch (operation) {
      case 'buy':
        await executeBuy(params[0], params[1]);
        break;
      case 'sell':
        await executeSell(params[0], params[1], params[2], params[3], params[4], params[5]);
        break;
      case 'refund':
        await executeRefund(params[0], params[1], params[2], params[3]);
        break;
      case 'dispute':
        await executeDispute(params[0], params[1], params[2]);
        break;
    }
  }, [executeBuy, executeSell, executeRefund, executeDispute]);

  // Computed values
  const isAnyOperationPending = Object.values(operations).some(op => op.status === 'pending');
  const pendingOperations = (Object.keys(operations) as EscrowOperationType[])
    .filter(op => operations[op].status === 'pending');

  const getOperationStatus = useCallback((operation: EscrowOperationType) => operations[operation].status, [operations]);
  const getOperationError = useCallback((operation: EscrowOperationType) => operations[operation].error, [operations]);
  const getOperationTxId = useCallback((operation: EscrowOperationType) => operations[operation].txId, [operations]);

  return {
    operations,
    isAnyOperationPending,
    pendingOperations,
    getOperationStatus,
    getOperationError,
    getOperationTxId,
    canExecuteOperation,
    getBlockingOperations,
    isOperationBlocked,
    validateOperation,
    executeBuy,
    executeSell,
    executeRefund,
    executeDispute,
    clearOperation,
    clearAllOperations,
    retryOperation
  };
};
