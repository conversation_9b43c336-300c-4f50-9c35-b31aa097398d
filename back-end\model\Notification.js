const { Model, DataTypes, Op } = require("sequelize");

class Notification extends Model {
    static initModel(sequelize) {
        return Notification.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                userId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                type: {
                    type: DataTypes.ENUM(
                        'dispute_created',
                        'dispute_assigned',
                        'dispute_resolved',
                        'trade_completed',
                        'trade_refunded',
                        'moderator_added',
                        'system_announcement',
                        'perk_purchased',
                        'perk_sold',
                        'escrow_created',
                        'escrow_released',
                        'trade_reported'
                    ),
                    allowNull: false,
                },
                title: {
                    type: DataTypes.STRING(255),
                    allowNull: false,
                },
                message: {
                    type: DataTypes.TEXT,
                    allowNull: false,
                },
                data: {
                    type: DataTypes.JSON,
                    allowNull: true,
                    comment: 'Additional data related to the notification (e.g., dispute ID, trade ID)',
                },
                isRead: {
                    type: DataTypes.BOOLEAN,
                    allowNull: false,
                    defaultValue: false,
                },
                priority: {
                    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
                    allowNull: false,
                    defaultValue: 'medium',
                },
                expiresAt: {
                    type: DataTypes.DATE,
                    allowNull: true,
                    comment: 'When the notification expires (optional)',
                },
                actionUrl: {
                    type: DataTypes.STRING(500),
                    allowNull: true,
                    comment: 'URL to navigate to when notification is clicked',
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'notifications',
                timestamps: true,
                indexes: [
                    {
                        fields: ['userId'],
                    },
                    {
                        fields: ['type'],
                    },
                    {
                        fields: ['isRead'],
                    },
                    {
                        fields: ['priority'],
                    },
                    {
                        fields: ['createdAt'],
                    },
                ],
            }
        );
    }

    static associate(models) {
        // Notification belongs to a user
        Notification.belongsTo(models.User, { 
            foreignKey: 'userId', 
            as: 'user' 
        });
    }

    // Instance method to mark as read
    async markAsRead() {
        this.isRead = true;
        await this.save();
    }

    // Static method to create dispute notification for all active moderators
    static async createDisputeNotification(disputeId, tradeId, initiatorRole, reason) {
        try {
            const { Moderator } = require('./index');
            
            // Get all active moderators
            const activeModerators = await Moderator.findAll({
                where: { isActive: true },
                include: [{ model: this.sequelize.models.User, as: 'user' }]
            });

            const notifications = activeModerators.map(moderator => ({
                userId: moderator.userId,
                type: 'dispute_created',
                title: 'New Dispute Created',
                message: `A new dispute has been initiated by the ${initiatorRole} for trade #${tradeId}. Reason: ${reason.substring(0, 100)}${reason.length > 100 ? '...' : ''}`,
                data: {
                    disputeId,
                    tradeId,
                    initiatorRole,
                    reason
                },
                priority: 'high',
                actionUrl: `/moderator/dashboard?dispute=${disputeId}`
            }));

            await this.bulkCreate(notifications);
            return notifications.length;
        } catch (error) {
            console.error('Error creating dispute notifications:', error);
            return 0;
        }
    }

    // Static method to create assignment notification
    static async createAssignmentNotification(disputeId, moderatorId, tradeId) {
        try {
            await this.create({
                userId: moderatorId,
                type: 'dispute_assigned',
                title: 'Dispute Assigned to You',
                message: `You have been assigned to resolve dispute for trade #${tradeId}. Please review the case details.`,
                data: {
                    disputeId,
                    tradeId
                },
                priority: 'high',
                actionUrl: `/moderator/dashboard?dispute=${disputeId}`
            });
        } catch (error) {
            console.error('Error creating assignment notification:', error);
        }
    }

    // Static method to create resolution notification
    static async createResolutionNotification(disputeId, buyerId, sellerId, resolution, tradeId, perkId, chatRoomId) {
        try {
            // Both notifications point to the same perk page with chat opened
            const tradeUrl = perkId && chatRoomId
                ? `/perks-shop/${perkId}?tradeId=${tradeId}&openChat=1&chatRoomId=${chatRoomId}`
                : `/perks-shop/${perkId || tradeId}?tradeId=${tradeId}`;

            const notifications = [
                {
                    userId: buyerId,
                    type: 'dispute_resolved',
                    title: 'Dispute Resolved',
                    message: `Your dispute for trade #${tradeId} has been resolved. Resolution: ${resolution.replace('resolved_', '').replace('_', ' ')}. Click to view chat.`,
                    data: {
                        disputeId,
                        tradeId,
                        perkId,
                        resolution,
                        chatRoomId
                    },
                    priority: 'high',
                    actionUrl: tradeUrl
                },
                {
                    userId: sellerId,
                    type: 'dispute_resolved',
                    title: 'Dispute Resolved',
                    message: `The dispute for trade #${tradeId} has been resolved. Resolution: ${resolution.replace('resolved_', '').replace('_', ' ')}. Click to view chat.`,
                    data: {
                        disputeId,
                        tradeId,
                        perkId,
                        resolution,
                        chatRoomId
                    },
                    priority: 'high',
                    actionUrl: tradeUrl
                }
            ];

            await this.bulkCreate(notifications);
        } catch (error) {
            console.error('Error creating resolution notifications:', error);
        }
    }

    // Static method to get unread count for a user
    static async getUnreadCount(userId) {
        try {
            return await this.count({
                where: {
                    userId,
                    isRead: false,
                    expiresAt: {
                        [Op.or]: [
                            null,
                            { [Op.gt]: new Date() }
                        ]
                    }
                }
            });
        } catch (error) {
            console.error('Error getting unread count:', error);
            return 0;
        }
    }

    // Static method to create perk purchase notifications
    static async createPerkPurchaseNotifications(buyerId, sellerId, perkName, price, tradeId, chatRoomId, perkId) {
        try {
            // Both notifications point to the same perk page with chat opened
            const tradeUrl = `/perks-shop/${perkId}?tradeId=${tradeId}&openChat=1&chatRoomId=${chatRoomId}`;

            const notifications = [
                {
                    userId: sellerId,
                    type: 'perk_sold',
                    title: 'New Perk Purchase!',
                    message: `Your perk "${perkName}" has been purchased for ${price} SOL. Click to open chat with buyer.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        price,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'high',
                    actionUrl: tradeUrl
                },
                {
                    userId: buyerId,
                    type: 'perk_purchased',
                    title: 'Perk Purchase Confirmed',
                    message: `You have successfully purchased "${perkName}" for ${price} SOL. Funds are now in escrow. Click to chat with seller.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        price,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'medium',
                    actionUrl: tradeUrl
                }
            ];

            await this.bulkCreate(notifications);
            return notifications.length;
        } catch (error) {
            console.error('Error creating perk purchase notifications:', error);
            return 0;
        }
    }

    // Static method to create seller release reminder notification
    static async createSellerReleaseReminderNotification(sellerId, buyerId, perkName, tradeId, chatRoomId, perkId) {
        try {
            const tradeUrl = `/perks-shop/${perkId}?tradeId=${tradeId}&openChat=1&chatRoomId=${chatRoomId}`;

            await this.create({
                userId: sellerId,
                type: 'escrow_release_reminder',
                title: 'Ready to Release Payment?',
                message: `The buyer has purchased your perk "${perkName}". After providing your service, click here to release the payment and complete the transaction.`,
                data: {
                    tradeId,
                    perkId,
                    perkName,
                    buyerId,
                    sellerId,
                    chatRoomId
                },
                priority: 'high',
                actionUrl: tradeUrl
            });
        } catch (error) {
            console.error('Error creating seller release reminder notification:', error);
        }
    }

    // Static method to create escrow release notifications
    static async createEscrowReleaseNotifications(buyerId, sellerId, perkName, tradeId, perkId, chatRoomId) {
        try {
            // Both notifications point to the same perk page with chat opened
            const tradeUrl = `/perks-shop/${perkId}?tradeId=${tradeId}&openChat=1&chatRoomId=${chatRoomId}`;

            const notifications = [
                {
                    userId: sellerId,
                    type: 'escrow_released',
                    title: 'Payment Released!',
                    message: `Funds for "${perkName}" have been released to you. Trade completed successfully! Click to view chat.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'high',
                    actionUrl: tradeUrl
                },
                {
                    userId: buyerId,
                    type: 'trade_completed',
                    title: 'Trade Completed!',
                    message: `Your purchase of "${perkName}" has been completed. Funds have been released to the seller. Click to view chat.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'medium',
                    actionUrl: tradeUrl
                }
            ];

            await this.bulkCreate(notifications);
            return notifications.length;
        } catch (error) {
            console.error('Error creating escrow release notifications:', error);
            return 0;
        }
    }

    // Static method to create trade refund notifications
    static async createTradeRefundNotifications(buyerId, sellerId, perkName, tradeId, perkId, chatRoomId) {
        try {
            // Both notifications point to the same perk page with chat opened
            const tradeUrl = `/perks-shop/${perkId}?tradeId=${tradeId}&openChat=1&chatRoomId=${chatRoomId}`;

            const notifications = [
                {
                    userId: buyerId,
                    type: 'trade_refunded',
                    title: 'Trade Refunded',
                    message: `Your purchase of "${perkName}" has been refunded. Funds have been returned to your wallet. Click to view chat.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'high',
                    actionUrl: tradeUrl
                },
                {
                    userId: sellerId,
                    type: 'trade_refunded',
                    title: 'Trade Refunded',
                    message: `The purchase of your perk "${perkName}" has been refunded to the buyer. Click to view chat.`,
                    data: {
                        tradeId,
                        perkId,
                        perkName,
                        buyerId,
                        sellerId,
                        chatRoomId
                    },
                    priority: 'medium',
                    actionUrl: tradeUrl
                }
            ];

            await this.bulkCreate(notifications);
            return notifications.length;
        } catch (error) {
            console.error('Error creating trade refund notifications:', error);
            return 0;
        }
    }

    // Static method to get notifications for a user
    static async getUserNotifications(userId, options = {}) {
        try {
            const { page = 1, pageSize = 20, unreadOnly = false } = options;
            const offset = (page - 1) * pageSize;

            const whereConditions = {
                userId,
                expiresAt: {
                    [Op.or]: [
                        null,
                        { [Op.gt]: new Date() }
                    ]
                }
            };

            if (unreadOnly) {
                whereConditions.isRead = false;
            }

            return await this.findAndCountAll({
                where: whereConditions,
                order: [['createdAt', 'DESC']],
                limit: parseInt(pageSize),
                offset: offset
            });
        } catch (error) {
            console.error('Error getting user notifications:', error);
            return { rows: [], count: 0 };
        }
    }
}

module.exports = Notification;
