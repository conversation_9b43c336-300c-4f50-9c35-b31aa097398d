import { useEffect, useRef, useState } from "react";
// @ts-ignore
import { io, Socket } from "socket.io-client";
import { useSocket } from "@/contexts/SocketProvider";

const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL || "http://localhost:8081";

export function useChatSocket({
  tradeId,
  chatRoomId,
  userId,
  wallet,
  onMessage,
  onTradeStatus,
}: {
  tradeId?: string | number;
  chatRoomId?: string;
  userId: string | number;
  wallet: string;
  onMessage: (msg: any) => void;
  onTradeStatus?: (status: { tradeId: string | number; status: string }) => void;
}) {
  const socket = useSocket();
  const [authenticated, setAuthenticated] = useState(false);
  const [joinedRoom, setJoinedRoom] = useState(false);
  const [tradeStatus, setTradeStatus] = useState<string | null>(null);
  const authenticatedRef = useRef(false);
  const joinedRoomRef = useRef(false);
  const lastTradeIdRef = useRef<string | number | null>(null);
  const lastWalletRef = useRef<string | null>(null);

  useEffect(() => {
    if (!socket) return;

    console.log('🔍 [useChatSocket] Effect triggered:', {
      wallet,
      lastWallet: lastWalletRef.current,
      authenticated: authenticatedRef.current,
      chatRoomId,
      tradeId
    });

    // Always authenticate if we have a wallet and aren't authenticated
    if (wallet && (!authenticatedRef.current || lastWalletRef.current !== wallet)) {
      console.log('🔍 [useChatSocket] Authenticating with wallet:', wallet);
      socket.emit("authenticate", { wallet });
      lastWalletRef.current = wallet;
      authenticatedRef.current = false;
    }
    const onAuthenticated = () => {
      console.log('✅ [useChatSocket] Authentication successful');
      setAuthenticated(true);
      authenticatedRef.current = true;

      // Only join room if chatRoomId or tradeId changes
      if (chatRoomId && lastTradeIdRef.current !== chatRoomId) {
        console.log('🔍 [useChatSocket] Joining room with chatRoomId:', chatRoomId);
        socket.emit("joinRoom", { chatRoomId });
        lastTradeIdRef.current = chatRoomId;
        joinedRoomRef.current = false;
      } else if (tradeId && lastTradeIdRef.current !== tradeId) {
        console.log('🔍 [useChatSocket] Joining room with tradeId:', tradeId);
        socket.emit("joinRoom", { tradeId });
        lastTradeIdRef.current = tradeId;
        joinedRoomRef.current = false;
      } else {
        console.log('🔍 [useChatSocket] No room to join or already joined');
      }
    };
    const onJoinedRoom = (data: any) => {
      console.log('🔍 [useChatSocket] Joined room:', data);
      setJoinedRoom(true);
      joinedRoomRef.current = true;

      // Handle redirect to correct chat room
      if (data.redirectToChatRoomId) {
        console.log('🔄 [useChatSocket] Server suggested redirect to:', data.redirectToChatRoomId);
        // You could emit an event to parent component to update the URL
      }
    };
    const onMessageHandler = (msg: any) => {
      console.log("[useChatSocket] Message received via socket:", msg);
      onMessage(msg);
    };
    const onTradeStatusHandler = (data: any) => {
      setTradeStatus(data.status);
      if (onTradeStatus) onTradeStatus(data);
    };
    const onError = (err: any) => {
      console.error("[useChatSocket] Socket error:", err);
    };
    socket.on("authenticated", onAuthenticated);
    socket.on("joinedRoom", onJoinedRoom);
    socket.on("message", onMessageHandler);
    socket.on("tradeStatus", onTradeStatusHandler);
    socket.on("error", onError);
    return () => {
      socket.off("authenticated", onAuthenticated);
      socket.off("joinedRoom", onJoinedRoom);
      socket.off("message", onMessageHandler);
      socket.off("tradeStatus", onTradeStatusHandler);
      socket.off("error", onError);
      authenticatedRef.current = false;
      joinedRoomRef.current = false;
    };
  }, [socket, tradeId, chatRoomId, wallet, onMessage, onTradeStatus]);

  // Send a message only if authenticated and joined room
  const sendMessage = (msg: { tradeId?: string | number; chatRoomId?: string; senderId: string | number; receiverId: string | number; message: string }) => {
    console.log("[useChatSocket] Attempting to send message:", {
      msg,
      authenticated,
      joinedRoom,
      canSend: authenticated && joinedRoom
    });

    if (authenticated && joinedRoom) {
      socket.emit("message", msg);
      console.log("[useChatSocket] Message sent via socket");
    } else {
      console.warn("[useChatSocket] Cannot send message - not authenticated or not joined room");
    }
  };

  // Emit release event
  const release = () => {
    if (authenticated && joinedRoom) {
      socket.emit("releasePerk", { tradeId }); // Changed from "release"
    }
  };

  // Emit report event
  const report = () => {
    if (authenticated && joinedRoom) {
      socket.emit("requestRefund", { tradeId }); // Changed from "report"
    }
  };

  return { sendMessage, release, report, authenticated, joinedRoom, tradeStatus };
} 
