const { Router } = require("express");
const {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  createAnnouncement
} = require("../controller/notification");
const { body, param, query } = require('express-validator');
const { validate } = require("../middleware/validator");
const { authenticate } = require("../middleware/authenticate");
const { requireAdmin } = require("../middleware/roleAuth");

const router = Router();

// Get notifications for authenticated user
router.get(
  "/",
  authenticate,
  [
    query("page")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Page must be a positive integer"),
    query("pageSize")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Page size must be between 1 and 100"),
    query("unreadOnly")
      .optional()
      .isBoolean()
      .withMessage("unreadOnly must be a boolean"),
  ],
  validate,
  getNotifications
);

// Get unread notification count
router.get(
  "/unread-count",
  authenticate,
  getUnreadCount
);

// Mark notification as read
router.patch(
  "/:notificationId/read",
  authenticate,
  [
    param("notificationId")
      .isInt({ min: 1 })
      .withMessage("Notification ID must be a positive integer"),
  ],
  validate,
  markAsRead
);

// Mark all notifications as read
router.patch(
  "/mark-all-read",
  authenticate,
  markAllAsRead
);

// Delete notification
router.delete(
  "/:notificationId",
  authenticate,
  [
    param("notificationId")
      .isInt({ min: 1 })
      .withMessage("Notification ID must be a positive integer"),
  ],
  validate,
  deleteNotification
);

// Create system announcement (admin only)
router.post(
  "/announcement",
  authenticate,
  requireAdmin,
  [
    body("title")
      .isLength({ min: 1, max: 255 })
      .withMessage("Title must be between 1 and 255 characters"),
    body("message")
      .isLength({ min: 1, max: 1000 })
      .withMessage("Message must be between 1 and 1000 characters"),
    body("priority")
      .optional()
      .isIn(['low', 'medium', 'high', 'urgent'])
      .withMessage("Priority must be low, medium, high, or urgent"),
    body("targetRole")
      .optional()
      .isIn(['all', 'moderators'])
      .withMessage("Target role must be all or moderators"),
    body("expiresAt")
      .optional()
      .isISO8601()
      .withMessage("Expires at must be a valid date"),
  ],
  validate,
  createAnnouncement
);

module.exports = router;
