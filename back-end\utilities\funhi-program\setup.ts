// require("ts-node/register");
// import { IdlAccounts, Program,Wallet } from "@coral-xyz/anchor";
// import { IDL, Funhi } from "./idl";
// import { clusterApiUrl, Connection } from "@solana/web3.js";
// const config = require("../../config/security");

// export const connection = new Connection(
//   config.NEXT_PUBLIC_RPC || clusterApiUrl("devnet"),
//   "confirmed",
// );

// // Initialize the program interface with the IDL, program ID, and connection.
// // This setup allows us to interact with the on-chain program using the defined interface.
// export const program: Program<Funhi> = new Program(IDL as unknown as Funhi, {
//   connection,
// });


// // This is just a TypeScript type for the Funhi data structure based on the IDL
// export type Global = IdlAccounts<Funhi>["Global"];
// export type BondingCurve = IdlAccounts<Funhi>["BondingCurve"];
// export type CreatorVault = IdlAccounts<Funhi>["CreatorVault"];
// export type LastWithdraw = IdlAccounts<Funhi>["LastWithdraw"];




// import * as anchor from "@coral-xyz/anchor";
// import { IdlAccounts } from "@coral-xyz/anchor";
// import { IDL, Funhi } from "./idl";
// import { clusterApiUrl, Connection, Keypair } from "@solana/web3.js";
// import bs58 from "bs58"; // Changed this line
// const config = require("../../config/security");

// const decoded = bs58.decode(process.env.ADMIN_PRIVATE_KEY);
// const keypair = Keypair.fromSecretKey(decoded);
// const wallet = new anchor.Wallet(keypair);

// export const connection = new Connection(
//   config.NEXT_PUBLIC_RPC || clusterApiUrl("devnet"),
//   "confirmed",
// );

// const provider = new anchor.AnchorProvider(connection, wallet, {
//   preflightCommitment: "confirmed",
// });

// anchor.setProvider(provider);

// export const program: anchor.Program<Funhi> = new anchor.Program(
//   IDL as anchor.Idl,
//   provider
// );

// export type Global = IdlAccounts<Funhi>["Global"];
// export type BondingCurve = IdlAccounts<Funhi>["BondingCurve"];
// export type CreatorVault = IdlAccounts<Funhi>["CreatorVault"];
// export type LastWithdraw = IdlAccounts<Funhi>["LastWithdraw"];



import * as anchor from "@coral-xyz/anchor";
import { IdlAccounts, Program } from "@coral-xyz/anchor";
import { IDL, Funhi } from "./idl";
import { clusterApiUrl, Connection, Keypair, PublicKey } from "@solana/web3.js";
import bs58 from "bs58";
const config = require("../../config/security");

const decoded = bs58.decode(config.ADMIN_PRIVATE_KEY);
const keypair = Keypair.fromSecretKey(decoded);
const wallet = new anchor.Wallet(keypair);

export const connection = new Connection(
  config.NEXT_PUBLIC_RPC || clusterApiUrl("devnet"),
  "confirmed",
);

const provider = new anchor.AnchorProvider(connection, wallet, {
  preflightCommitment: "confirmed",
});

anchor.setProvider(provider);

// Initialize the program with the modified IDL
export const program: Program<Funhi> = new Program(
  IDL as any,
  provider
);

// Debug output
console.log("Program initialized with ID:", program.programId.toString());

// Account types
export type Global = IdlAccounts<Funhi>["global"];
export type BondingCurve = IdlAccounts<Funhi>["bondingCurve"];
export type CreatorVault = IdlAccounts<Funhi>["creatorVault"];
export type LastWithdraw = IdlAccounts<Funhi>["lastWithdraw"];
