import { Message } from "@/components/ui/runningLine/types";
import { useEffect, useRef, useState } from "react";
import { fetchFormattedUserLogs } from "@/utils/helpers";
import { useUnreadChatMessages, useAppContext } from '@/contexts/AppContext';
import { useTranslation } from '@/hooks/useTranslation';






interface NotificationModalProps {
  onClose: () => void;
  onOpenChat: (chatRoomId: string, receiverId: number) => void;
}
const NotificationModal = ({ onClose, onOpenChat }: NotificationModalProps) => {
  const { t } = useTranslation();

    const [selectedTab, setSelectedTab] = useState<'system' | 'trade'>('system');
    const [systemNotifications, setSystemNotifications] = useState<Message[]>([]);
    const [tradeNotifications, setTradeNotifications] = useState<Message[]>([]);
    const modalRef = useRef<HTMLDivElement>(null);
    const { unreadChatMessages } = useUnreadChatMessages();
    const { setUnreadSystemNotificationsCount } = useAppContext();

    useEffect(() => {
        const fetchLogs = async () => {
            const userBoStr = localStorage.getItem("userBo");
            if (userBoStr) {
                try {
                    const userBo = JSON.parse(userBoStr);
                    const userId = userBo.id;
                    console.log("Fetching notifications for user:", userId);
                    
                    // Fetch system notifications (type "1") - system-wide activity
                    const systemData = await fetchFormattedUserLogs("0", "1");
                    console.log("System notifications:", systemData);
                    setSystemNotifications(systemData);
                    setUnreadSystemNotificationsCount(systemData.length);
                    
                    // Fetch trade notifications (type "0") - user's own activity + chat messages
                    const tradeData = await fetchFormattedUserLogs(userId, "0");
                    console.log("Trade notifications:", tradeData);
                    setTradeNotifications(tradeData);
                } catch (e) {
                    console.error("Error fetching notifications:", e);
                }
            } else {
                console.log("No userBo found in localStorage");
            }
        };
        fetchLogs();
    }, []);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
                onClose();
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose]);

    const getTypeStyles = (type: Message['type']) => {
        switch (type) {
            case 'buy':
                return {
                    bg: 'bg-green-50 hover:bg-green-100',
                    border: 'border-l-4 border-l-green-500',
                    icon: 'text-green-600',
                    typeText: 'text-green-700 bg-green-100'
                };
            case 'sell':
                return {
                    bg: 'bg-red-50 hover:bg-red-100',
                    border: 'border-l-4 border-l-red-500',
                    icon: 'text-red-600',
                    typeText: 'text-red-700 bg-red-100'
                };
            case 'airdrop':
                return {
                    bg: 'bg-blue-50 hover:bg-blue-100',
                    border: 'border-l-4 border-l-blue-500',
                    icon: 'text-blue-600',
                    typeText: 'text-blue-700 bg-blue-100'
                };
            case 'burn':
                return {
                    bg: 'bg-orange-50 hover:bg-orange-100',
                    border: 'border-l-4 border-l-orange-500',
                    icon: 'text-orange-600',
                    typeText: 'text-orange-700 bg-orange-100'
                };
            case 'general':
                return {
                    bg: 'bg-gray-50 hover:bg-gray-100',
                    border: 'border-l-4 border-l-gray-500',
                    icon: 'text-gray-600',
                    typeText: 'text-gray-700 bg-gray-100'
                };
            default:
                return {
                    bg: 'bg-gray-50 hover:bg-gray-100',
                    border: 'border-l-4 border-l-gray-500',
                    icon: 'text-gray-600',
                    typeText: 'text-gray-700 bg-gray-100'
                };
        }
    };

    const formatTimeAgo = (timestamp: Date) => {
        const now = new Date();
        const diffInSeconds = Math.floor((now.getTime() - timestamp.getTime()) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
    };

    // Helper to get notifications for the current tab
    let notifications: Message[] = selectedTab === 'trade' ? tradeNotifications : systemNotifications;
    
    // For trade tab, also include unread chat messages from the context
    if (selectedTab === 'trade' && unreadChatMessages.length > 0) {
      // Add unread chat messages as trade notifications (avoid duplicates by id)
      const chatNotifs: Message[] = unreadChatMessages.map((msg: any, index: number) => ({
        id: msg.id ? `chat-${msg.id}` : `chat-fallback-${msg.chatRoomId}-${msg.senderId}-${msg.createdAt}-${index}`,
        text: `New message: ${msg.message}`,
        type: 'general',
        icon: '💬',
        timestamp: new Date(msg.createdAt),
        tradeId: msg.tradeId,
        receiverId: msg.receiverId,
        chatRoomId: msg.chatRoomId, // Use the actual chatRoomId from the message
      }));
      // Avoid duplicate ids
      const existingIds = new Set(notifications.map(n => n.id));
      notifications = [...chatNotifs.filter(n => !existingIds.has(n.id)), ...notifications];
    }

    // Patch: For trade tab, show 'Order #id' for general messages with trade context
    if (selectedTab === 'trade') {
      notifications = notifications.map((notification) => {
        if (
          notification.type === 'general' &&
          (notification.tradeId || notification.chatRoomId)
        ) {
          // Prefer tradeId, fallback to id if not present
          const orderId = notification.tradeId || notification.id;
          return {
            ...notification,
            text: `Order #${orderId}`,
          };
        }
        return notification;
      });
    }

    return (
        <div className="fixed inset-0 flex items-start justify-end bg-black/20 backdrop-blur-sm z-50">
            <div 
                className="flex flex-col bg-white rounded-xl shadow-2xl w-[420px] max-h-[600px] mt-20 mr-6 border border-gray-200 overflow-hidden" 
                ref={modalRef}
            >
                {/* Header with Tabs */}
                <div className="flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-[#FF6600] to-[#FF8533]">
                    <div>
                        <h2 className="text-white font-semibold text-lg">{t('navigation.notifications')}</h2>
                        <div className="flex gap-2 mt-2">
                            <button
                                className={`px-3 py-1 rounded-full text-sm font-medium ${selectedTab === 'trade' ? 'bg-white text-[#FF6600]' : 'bg-white/20 text-white'}`}
                                onClick={() => setSelectedTab('trade')}
                            >
                                {t('notifications.trade')}
                            </button>
                            <button
                                className={`px-3 py-1 rounded-full text-sm font-medium ${selectedTab === 'system' ? 'bg-white text-[#FF6600]' : 'bg-white/20 text-white'}`}
                                onClick={() => setSelectedTab('system')}
                            >
                                {t('notifications.system')}
                            </button>
                        </div>
                    </div>
                    <button 
                        onClick={onClose}
                        className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-200 group"
                    >
                        <svg className="w-4 h-4 text-white group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* Notifications List (uses notifications for current tab) */}
                <div className="flex-1 overflow-y-auto max-h-[500px]">
                    {notifications.length === 0 ? (
                        <div className="flex flex-col items-center justify-center p-8 text-gray-500">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                </svg>
                            </div>
                            <h3 className="font-medium text-gray-900 mb-2">{t('notifications.noNotifications')}</h3>
                            <p className="text-sm text-gray-500 text-center">{t('notifications.allCaughtUp')}</p>
                        </div>
                    ) : (
                        <div className="p-4 space-y-3">
                            {notifications.map((notification: Message, index: number) => {
                                const styles = getTypeStyles(notification.type);
                                return (
                                    <div
                                        key={notification.id || `notif-fallback-${index}`}
                                        className={`${styles.bg} ${styles.border} p-4 rounded-lg transition-all duration-300 hover:shadow-md hover:scale-[1.02] cursor-pointer group border border-gray-100`}
                                        style={{
                                            animationDelay: `${index * 100}ms`
                                        }}
                                        onClick={() => {
                                          const n = notification as any;
                                          console.log('Notification clicked:', n);
                                          console.log('chatRoomId:', n.chatRoomId, 'receiverId:', n.receiverId);
                                          
                                          if (n.chatRoomId && n.receiverId) {
                                            console.log('Opening chat with chatRoomId:', n.chatRoomId);
                                            onOpenChat(n.chatRoomId, n.receiverId);
                                            onClose();
                                          }
                                        }}
                                    >
                                        <div className="flex items-start gap-3">
                                            <div className={`w-10 h-10 rounded-full flex items-center justify-center text-xl ${styles.icon} bg-white/60 group-hover:bg-white/80 transition-all duration-200`}>
                                                {notification.icon}
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-gray-900 font-medium text-sm leading-relaxed group-hover:text-gray-800 transition-colors duration-200">
                                                    {notification.text}
                                                </p>
                                                <div className="flex items-center justify-between mt-2">
                                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${styles.typeText} capitalize`}>
                                                        {notification.type}
                                                    </span>
                                                    <span className="text-xs text-gray-500 font-medium">
                                                        {notification.timestamp ? formatTimeAgo(notification.timestamp) : 'Just now'}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="w-2 h-2 bg-[#FF6600] rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>

                {/* Footer */}
                <div className="p-4 border-t border-gray-100 bg-gray-50">
                    <button className="w-full py-2 px-4 bg-[#FF6600] hover:bg-[#E55A00] text-white font-medium rounded-lg transition-all duration-200 hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98]">
                        {t('notifications.markAllAsRead')}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default NotificationModal;   