"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, CheckCircle, AlertCircle, Clock, Wallet, Network, DollarSign } from 'lucide-react';
import { EscrowOperationType } from '@/utils/escrow';

// Professional Loading Spinner Component
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const colorClasses = {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    danger: 'text-red-600'
  };

  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      className={`${sizeClasses[size]} ${colorClasses[color]} ${className}`}
    >
      <Loader2 className="w-full h-full" />
    </motion.div>
  );
};

// Operation-specific Loading Messages
const getOperationConfig = (operation: EscrowOperationType) => {
  const configs = {
    buy: {
      icon: <DollarSign className="w-5 h-5" />,
      title: 'Creating Escrow Transaction',
      message: 'Preparing your purchase and setting up escrow protection...',
      steps: [
        'Validating transaction details',
        'Creating escrow account',
        'Securing your funds',
        'Finalizing transaction'
      ]
    },
    sell: {
      icon: <CheckCircle className="w-5 h-5" />,
      title: 'Releasing Escrow Funds',
      message: 'Processing payment release to complete the transaction...',
      steps: [
        'Verifying escrow conditions',
        'Transferring perk tokens',
        'Releasing payment',
        'Completing transaction'
      ]
    },
    refund: {
      icon: <AlertCircle className="w-5 h-5" />,
      title: 'Processing Refund',
      message: 'Returning your funds from the escrow account...',
      steps: [
        'Validating refund request',
        'Closing escrow account',
        'Returning funds',
        'Completing refund'
      ]
    },
    dispute: {
      icon: <Clock className="w-5 h-5" />,
      title: 'Initiating Dispute',
      message: 'Submitting your dispute for moderator review...',
      steps: [
        'Recording dispute details',
        'Notifying moderators',
        'Locking escrow funds',
        'Awaiting review'
      ]
    }
  };

  return configs[operation];
};

// Enhanced Loading Modal
interface EscrowLoadingModalProps {
  isOpen: boolean;
  operation: EscrowOperationType;
  currentStep?: number;
  customMessage?: string;
  showSteps?: boolean;
  onCancel?: () => void;
  canCancel?: boolean;
}

export const EscrowLoadingModal: React.FC<EscrowLoadingModalProps> = ({
  isOpen,
  operation,
  currentStep = 0,
  customMessage,
  showSteps = true,
  onCancel,
  canCancel = false
}) => {
  const config = getOperationConfig(operation);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6"
        >
          {/* Header */}
          <div className="text-center mb-6">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
              className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4"
            >
              <div className="text-blue-600">
                {config.icon}
              </div>
            </motion.div>
            
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {config.title}
            </h3>
            
            <p className="text-gray-600 text-sm">
              {customMessage || config.message}
            </p>
          </div>

          {/* Loading Spinner */}
          <div className="flex justify-center mb-6">
            <LoadingSpinner size="lg" color="primary" />
          </div>

          {/* Progress Steps */}
          {showSteps && (
            <div className="space-y-3 mb-6">
              {config.steps.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`flex items-center space-x-3 ${
                    index <= currentStep ? 'text-blue-600' : 'text-gray-400'
                  }`}
                >
                  <div className={`w-2 h-2 rounded-full ${
                    index < currentStep ? 'bg-green-500' :
                    index === currentStep ? 'bg-blue-500 animate-pulse' :
                    'bg-gray-300'
                  }`} />
                  <span className="text-sm">{step}</span>
                  {index < currentStep && (
                    <CheckCircle className="w-4 h-4 text-green-500 ml-auto" />
                  )}
                </motion.div>
              ))}
            </div>
          )}

          {/* Cancel Button */}
          {canCancel && onCancel && (
            <div className="text-center">
              <button
                onClick={onCancel}
                className="text-gray-500 hover:text-gray-700 text-sm font-medium transition-colors"
              >
                Cancel Operation
              </button>
            </div>
          )}

          {/* Warning for important operations */}
          {(operation === 'buy' || operation === 'sell') && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-xs text-yellow-800">
                <strong>Important:</strong> Please don't close this window or refresh the page while the transaction is processing.
              </p>
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

// Inline Loading Component for Buttons
interface InlineLoadingProps {
  operation: EscrowOperationType;
  size?: 'sm' | 'md';
  showText?: boolean;
}

export const InlineLoading: React.FC<InlineLoadingProps> = ({
  operation,
  size = 'sm',
  showText = true
}) => {
  const config = getOperationConfig(operation);
  const loadingTexts = {
    buy: 'Creating...',
    sell: 'Releasing...',
    refund: 'Refunding...',
    dispute: 'Submitting...'
  };

  return (
    <div className="flex items-center space-x-2">
      <LoadingSpinner size={size} color="primary" />
      {showText && (
        <span className="text-sm font-medium">
          {loadingTexts[operation]}
        </span>
      )}
    </div>
  );
};

// Network Status Indicator
interface NetworkStatusProps {
  isConnected: boolean;
  isHealthy: boolean;
  className?: string;
}

export const NetworkStatus: React.FC<NetworkStatusProps> = ({
  isConnected,
  isHealthy,
  className = ''
}) => {
  const getStatusConfig = () => {
    if (!isConnected) {
      return {
        color: 'text-red-500',
        bgColor: 'bg-red-100',
        icon: <Network className="w-4 h-4" />,
        text: 'Disconnected'
      };
    }
    
    if (!isHealthy) {
      return {
        color: 'text-yellow-500',
        bgColor: 'bg-yellow-100',
        icon: <Network className="w-4 h-4" />,
        text: 'Poor Connection'
      };
    }
    
    return {
      color: 'text-green-500',
      bgColor: 'bg-green-100',
      icon: <Network className="w-4 h-4" />,
      text: 'Connected'
    };
  };

  const config = getStatusConfig();

  return (
    <div className={`inline-flex items-center space-x-2 px-2 py-1 rounded-full ${config.bgColor} ${className}`}>
      <div className={config.color}>
        {config.icon}
      </div>
      <span className={`text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    </div>
  );
};

// Wallet Status Indicator
interface WalletStatusProps {
  isConnected: boolean;
  address?: string;
  balance?: number;
  className?: string;
}

export const WalletStatus: React.FC<WalletStatusProps> = ({
  isConnected,
  address,
  balance,
  className = ''
}) => {
  if (!isConnected) {
    return (
      <div className={`inline-flex items-center space-x-2 px-3 py-2 bg-red-100 text-red-700 rounded-lg ${className}`}>
        <Wallet className="w-4 h-4" />
        <span className="text-sm font-medium">Wallet Not Connected</span>
      </div>
    );
  }

  return (
    <div className={`inline-flex items-center space-x-2 px-3 py-2 bg-green-100 text-green-700 rounded-lg ${className}`}>
      <Wallet className="w-4 h-4" />
      <div className="text-sm">
        <div className="font-medium">
          {address ? `${address.slice(0, 4)}...${address.slice(-4)}` : 'Connected'}
        </div>
        {balance !== undefined && (
          <div className="text-xs opacity-75">
            {balance.toFixed(4)} SOL
          </div>
        )}
      </div>
    </div>
  );
};

// Professional Token Details Loading Component
interface TokenDetailsLoadingProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showIcon?: boolean;
}

export const TokenDetailsLoading: React.FC<TokenDetailsLoadingProps> = ({
  message = "Loading token details...",
  size = 'lg',
  className = '',
  showIcon = true
}) => {
  const sizeConfig = {
    sm: {
      container: 'py-8',
      spinner: 'md' as const,
      text: 'text-sm',
      icon: 'w-5 h-5'
    },
    md: {
      container: 'py-12',
      spinner: 'lg' as const,
      text: 'text-base',
      icon: 'w-6 h-6'
    },
    lg: {
      container: 'py-20',
      spinner: 'xl' as const,
      text: 'text-lg',
      icon: 'w-8 h-8'
    }
  };

  const config = sizeConfig[size];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className={`flex flex-col items-center justify-center ${config.container} ${className}`}
    >
      {/* Icon and Spinner Container */}
      <div className="relative mb-6">
        {showIcon && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.1, type: 'spring', stiffness: 200 }}
            className="absolute inset-0 flex items-center justify-center"
          >
            <div className="bg-blue-100 rounded-full p-4">
              <DollarSign className={`${config.icon} text-blue-600`} />
            </div>
          </motion.div>
        )}

        {/* Animated Ring */}
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
          className="w-20 h-20 border-4 border-blue-200 border-t-blue-600 rounded-full"
        />
      </div>

      {/* Loading Text */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="text-center"
      >
        <h3 className={`font-semibold text-gray-900 mb-2 ${config.text}`}>
          {message}
        </h3>
        <p className="text-gray-600 text-sm max-w-md">
          Please wait while we fetch the latest token information and market data.
        </p>
      </motion.div>

      {/* Animated Dots */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="flex space-x-1 mt-4"
      >
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: index * 0.2
            }}
            className="w-2 h-2 bg-blue-600 rounded-full"
          />
        ))}
      </motion.div>

      {/* Progress Bar */}
      <motion.div
        initial={{ width: 0 }}
        animate={{ width: '100%' }}
        transition={{ duration: 3, repeat: Infinity }}
        className="mt-6 h-1 bg-blue-600 rounded-full"
        style={{ width: '200px', maxWidth: '200px' }}
      />
    </motion.div>
  );
};

// Compact Token Loading Component for smaller spaces
interface CompactTokenLoadingProps {
  message?: string;
  className?: string;
}

export const CompactTokenLoading: React.FC<CompactTokenLoadingProps> = ({
  message = "Loading...",
  className = ''
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`flex items-center justify-center space-x-3 p-4 ${className}`}
    >
      <LoadingSpinner size="md" color="primary" />
      <div className="text-center">
        <p className="text-gray-700 font-medium">{message}</p>
        <div className="flex space-x-1 mt-2 justify-center">
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              animate={{
                scale: [1, 1.3, 1],
                opacity: [0.4, 1, 0.4]
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                delay: index * 0.15
              }}
              className="w-1.5 h-1.5 bg-blue-500 rounded-full"
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
};

// Generic Page Loading Component
interface PageLoadingProps {
  title?: string;
  subtitle?: string;
  className?: string;
  showProgress?: boolean;
}

export const PageLoading: React.FC<PageLoadingProps> = ({
  title = "Loading...",
  subtitle = "Please wait while we prepare your content",
  className = '',
  showProgress = true
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`min-h-screen bg-gray-50 flex items-center justify-center ${className}`}
    >
      <div className="text-center max-w-md mx-auto px-6">
        {/* Main Loading Animation */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: 'spring', stiffness: 200, delay: 0.1 }}
          className="relative mb-8"
        >
          <div className="w-24 h-24 mx-auto relative">
            {/* Outer Ring */}
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 3, repeat: Infinity, ease: 'linear' }}
              className="absolute inset-0 border-4 border-blue-200 border-t-blue-600 rounded-full"
            />

            {/* Inner Ring */}
            <motion.div
              animate={{ rotate: -360 }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              className="absolute inset-2 border-4 border-gray-200 border-b-gray-400 rounded-full"
            />

            {/* Center Icon */}
            <div className="absolute inset-0 flex items-center justify-center">
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="bg-white rounded-full p-3 shadow-lg"
              >
                <Loader2 className="w-6 h-6 text-blue-600" />
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Text Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">{title}</h2>
          <p className="text-gray-600 mb-6">{subtitle}</p>
        </motion.div>

        {/* Progress Indicator */}
        {showProgress && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="space-y-3"
          >
            <div className="flex justify-center space-x-2">
              {[0, 1, 2, 3, 4].map((index) => (
                <motion.div
                  key={index}
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.3, 1, 0.3]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: index * 0.1
                  }}
                  className="w-2 h-2 bg-blue-600 rounded-full"
                />
              ))}
            </div>

            {/* Progress Bar */}
            <div className="w-64 h-1 bg-gray-200 rounded-full mx-auto overflow-hidden">
              <motion.div
                animate={{ x: ['-100%', '100%'] }}
                transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                className="h-full w-1/3 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full"
              />
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

// Pulse Loading Component for content placeholders
interface PulseLoadingProps {
  lines?: number;
  className?: string;
  showAvatar?: boolean;
}

export const PulseLoading: React.FC<PulseLoadingProps> = ({
  lines = 3,
  className = '',
  showAvatar = false
}) => {
  return (
    <div className={`animate-pulse ${className}`}>
      <div className="flex space-x-4">
        {showAvatar && (
          <div className="rounded-full bg-gray-300 h-12 w-12"></div>
        )}
        <div className="flex-1 space-y-3">
          {Array.from({ length: lines }).map((_, index) => (
            <div
              key={index}
              className={`h-4 bg-gray-300 rounded ${
                index === lines - 1 ? 'w-3/4' : 'w-full'
              }`}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Skeleton Card Loading Component
interface SkeletonCardProps {
  className?: string;
  showImage?: boolean;
  showButton?: boolean;
}

export const SkeletonCard: React.FC<SkeletonCardProps> = ({
  className = '',
  showImage = true,
  showButton = true
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 animate-pulse ${className}`}>
      {showImage && (
        <div className="h-48 bg-gray-300 rounded-lg mb-4"></div>
      )}

      <div className="space-y-3">
        <div className="h-6 bg-gray-300 rounded w-3/4"></div>
        <div className="h-4 bg-gray-300 rounded w-full"></div>
        <div className="h-4 bg-gray-300 rounded w-5/6"></div>

        {showButton && (
          <div className="pt-4">
            <div className="h-10 bg-gray-300 rounded w-32"></div>
          </div>
        )}
      </div>
    </div>
  );
};
