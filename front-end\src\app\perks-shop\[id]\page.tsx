"use client";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { buyPerk, fetchPerkDetails, refundPerk, releasePerk, confirmEscrow, reportTrade, getUserTrades } from "../../../axios/requests";
import axios from "axios";
import { API_CONFIG } from "@/config/environment";
import { useAppContext } from "../../../contexts/AppContext";
import { getTokenOutputForSolInput } from "../../../utils/helpers";
import ChatModal from "@/components/shared/chat/ChatModal";
import { APP_CONFIG } from "@/config/environment";
import dynamic from "next/dynamic";
import { usePrivy } from "@privy-io/react-auth";
import { EscrowTxData, canInitiateDispute, getDisputeStatusInfo } from "@/utils/escrow";
import { useWallet } from "@/hooks/useWallet";
import { useEscrowOperations } from "@/hooks/useEscrowOperations";
import { EscrowLoadingButton, EscrowOperationStatus, EscrowErrorDisplay, EscrowActionCard } from "@/components/ui/EscrowComponents";
import { useEscrowErrorManager, showEscrowError, showEscrowLoading, hideAllEscrowModals } from "@/components/ui/EscrowErrorManager";
import { TokenDetailsLoading } from "@/components/ui/LoadingComponents";
import { useCongratulationsModal } from "@/hooks/useCongratulationsModal";
import { CongratulationsModal } from "@/components/ui/CongratulationsModal";
import DisputeModal from "@/components/shared/dispute/DisputeModal";

const TradeChart = dynamic(() => import('@/components/shared/chart'), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});

const Detail = dynamic(() => import('@/components/shared/detail').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});


const ProductCard = dynamic(() => import('@/components/shared/product-card').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});


const Review = dynamic(() => import('@/components/shared/reviews').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});


const TokenStats = dynamic(() => import('@/components/shared/token-stats').then(mod => ({ default: mod.default })), {
  loading: () => <div className="animate-pulse h-96 bg-gray-200 rounded-lg"></div>,
});



export default function PerksDetailsPage() {
  const [perkData, setPerkData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const [bonusTokens, setBonusTokens] = useState(0);
  const [solInput, setSolInput] = useState(0);
  const [activeTrade, setActiveTrade] = useState<any>(null);
  const [disputeModalOpen, setDisputeModalOpen] = useState(false);
  const [disputeInitiatorRole, setDisputeInitiatorRole] = useState<'buyer' | 'seller'>('buyer');
  const router = useRouter();
  const { user } = usePrivy();
  const { solanaWallet, isConnected } = useWallet();


  const { state } = useAppContext();
  const params = useParams();
  const searchParams = useSearchParams();
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [chatRoomId, setChatRoomId] = useState<string | null>(null);

  // Initialize congratulations modal
  const congratulationsModal = useCongratulationsModal({
    onNavigateToChat: (chatRoomId: string) => {
      setChatRoomId(chatRoomId);
      setIsChatOpen(true);
      router.push(`?openChat=1&chatRoomId=${chatRoomId}`);
    },
    onViewTransaction: (txId: string) => {
      // Could navigate to transaction details page
      console.log('View transaction:', txId);
    },
    onShareSuccess: (type, txId) => {
      // Could implement sharing functionality
      console.log('Share success:', type, txId);
    },
    onContinueShopping: () => {
      router.push('/perks-shop');
    }
  });

  // Initialize error manager
  const errorManager = useEscrowErrorManager({
    onRetry: (operation) => {
      console.log(`Retrying ${operation} operation`);
      // The retry logic is handled by the escrow operations hook
    },
    onHelp: (category, operation) => {
      console.log(`Help requested for ${category} in ${operation} operation`);
      // Could open help documentation or support chat
    },
    onProceedAnyway: (operation) => {
      console.log(`Proceeding anyway with ${operation} operation`);
      // Retry the operation with validation skipped
      if (operation === 'buy' && perkData && solanaWallet) {
        handlePurchaseWithoutValidation();
      }
    },
    onWalletReconnect: () => {
      // Trigger wallet reconnection
      window.location.reload(); // Simple approach, could be more sophisticated
    }
  });

  // Initialize escrow operations hook
  const escrowOperations = useEscrowOperations({
    onOperationStart: (operation) => {
      showEscrowLoading(errorManager, operation, 0);
    },
    onOperationSuccess: (operation, result) => {
      console.log(`${operation} operation completed successfully:`, result);
      hideAllEscrowModals(errorManager);

      // Handle specific operation success cases
      if (operation === 'buy' && typeof result === 'string') {
        // Update trade data after successful purchase
        setActiveTrade((prev: any) => prev ? { ...prev, status: 'escrowed', txId: result } : null);
      } else if (operation === 'sell' && typeof result === 'string') {
        // Update trade data after successful release
        setActiveTrade((prev: any) => prev ? { ...prev, status: 'released', txId: result } : null);
      } else if (operation === 'refund' && typeof result === 'string') {
        // Update trade data after successful refund
        setActiveTrade((prev: any) => prev ? { ...prev, status: 'refunded', txId: result } : null);
      } else if (operation === 'dispute') {
        // Update trade data after successful dispute initiation
        setActiveTrade((prev: any) => prev ? {
          ...prev,
          disputeStatus: 'open',
          dispute: result
        } : null);
      }
    },
    onOperationError: (operation, detectedError) => {
      console.error(`${operation} operation failed:`, detectedError);
      hideAllEscrowModals(errorManager);
      showEscrowError(errorManager, detectedError.message, operation, {
        walletAddress: solanaWallet?.address
      });
    },
    onShowCongratulations: (operation, txId, amount, additionalData) => {
      // Show congratulations modal for successful operations
      congratulationsModal.showEscrowSuccess(operation, txId, amount, additionalData);
    },
    showToasts: false, // We're using modals instead
    showModals: true,
    showCongratulations: true
  });

  const idParam = params?.id;
  const id = Array.isArray(idParam) ? idParam[0] : idParam ?? '0';

  // Initialize chatRoomId from URL params - let the socket handle validation
  useEffect(() => {
    const urlChatRoomId = searchParams.get("chatRoomId");
    console.log('🔍 [PerkPage] Setting chatRoomId from URL:', urlChatRoomId);

    if (urlChatRoomId) {
      setChatRoomId(urlChatRoomId);
    }
  }, [searchParams]);

  useEffect(() => {
    const openChat = searchParams.get("openChat");
    console.log('🔍 [PerkPage] OpenChat effect:', { openChat });

    if (openChat === "1") {
      const urlChatRoomId = searchParams.get("chatRoomId");
      console.log('🔍 [PerkPage] Opening chat with chatRoomId:', urlChatRoomId);

      if (urlChatRoomId) {
        setChatRoomId(urlChatRoomId);
        setIsChatOpen(true);
      }
    }
  }, [searchParams]);

  useEffect(() => {
    const loadPerkData = async () => {
      if (id) {
        setLoading(true);
        try {
          const userId = state?.userBo?.id;
          const response = await fetchPerkDetails(id, userId);
          if (response.status === 200) {
            setPerkData(response.data);

            // Load existing trade data if user is logged in
            if (userId) {
              await loadExistingTrade(userId, response.data.perkId);
            }
          } else {
            console.error("Failed to fetch token:", response);
          }
        } catch (error) {
          console.error("Error fetching token data:", error);
        } finally {
          setLoading(false);
        }
      }
    };


    loadPerkData();
  }, [state?.userBo]);

  // Load existing trade data for this perk and user
  const loadExistingTrade = async (userId: number, perkId: string) => {
    try {
      console.log('🔍 [LoadTrade] Loading existing trades for user:', userId, 'perkId:', perkId);
      const trades = await getUserTrades(userId.toString());

      // Find active trade for this perk
      const activePerkTrade = trades.find((trade: any) =>
        trade.perkId === perkId &&
        (trade.status === 'escrowed' || trade.status === 'pending_signature')
      );

      if (activePerkTrade) {
        console.log('✅ [LoadTrade] Found existing trade:', activePerkTrade);

        // Reconstruct the trade data with necessary fields
        const tradeData = {
          ...activePerkTrade,
          // Ensure we have the necessary fields for escrow operations
          escrowId: activePerkTrade.escrowId,
          from: activePerkTrade.from, // buyer wallet
          to: activePerkTrade.to,     // seller wallet
          tradeId: activePerkTrade.id
        };

        setActiveTrade(tradeData);
        console.log('🔄 [LoadTrade] Set activeTrade from existing data');
      } else {
        console.log('ℹ️ [LoadTrade] No active trade found for this perk');
      }
    } catch (error) {
      console.error('❌ [LoadTrade] Error loading existing trade:', error);
    }
  };

  useEffect(() => {
    const getTokenAmount = async () => {
      if (!perkData || !perkData.token || !perkData.token.tokenAddress || !perkData.price) {
        return;
      }

      const conversionRate = Number(APP_CONFIG.CONVERSION_RATE) || 155;
      const solInput = perkData.price / conversionRate;

      try {
        // Check if this is a bonding curve token or a perk token
        const price = await getTokenOutputForSolInput(perkData.token.tokenAddress, solInput, 0.05);
        
        if (price.estimatedOutput === 0) {
          // This is likely a perk token without bonding curve
          // Set bonus tokens based on perk-specific logic or fixed rate
          setBonusTokens(0); // Or calculate based on perk value
          setSolInput(solInput);
        } else {
          setBonusTokens(price.estimatedOutput);
          setSolInput(solInput);
        }
      } catch (err) {
        console.error('Error estimating token output:', err);
        // Graceful fallback for perk tokens
        setBonusTokens(0);
        setSolInput(solInput);
      }
    };

    getTokenAmount();
  }, [perkData]);

  // Handle purchase without validation (for "Proceed Anyway" functionality)
  const handlePurchaseWithoutValidation = async () => {
    if (!isConnected || !solanaWallet) {
      showEscrowError(errorManager, "Please connect your wallet to make a purchase", 'buy', {
        walletAddress: null
      });
      return;
    }

    if (!state.userBo || !state.userBo.id) {
      showEscrowError(errorManager, "Please log in to make a purchase", 'buy');
      return;
    }

    try {
      setLoading(true);
      console.log("Starting escrow purchase (without validation)...");

      // Step 1: Prepare escrow transaction on backend
      const response = await buyPerk({
        userId: state.userBo.id,
        perkId: perkData.perkId,
        price: perkData.price,
        buyerWallet: solanaWallet.address,
        sellerWallet: perkData.user.privywallet
      });

      if (response.status === 200) {
        console.log("Escrow prepared:", response.data);
        const { tradeId, escrowTxData } = response.data;

        // Step 2: Sign and execute escrow transaction using the hook (skip validation)
        console.log("Signing escrow transaction...");
        const txId = await escrowOperations.executeBuy(
          escrowTxData as EscrowTxData,
          solanaWallet,
          true // Skip validation
        );

        // Step 3: Confirm transaction on backend
        console.log("Confirming escrow transaction...");
        await confirmEscrow({ tradeId, txId });

        // Step 4: Set trade data and navigate to chat
        const tradeData = {
          ...response.data,
          status: 'escrowed',
          txId,
          // Extract necessary fields from escrowTxData for easy access
          escrowId: response.data.escrowTxData?.escrowId,
          from: response.data.escrowTxData?.buyerWallet, // buyer wallet
          to: response.data.escrowTxData?.sellerWallet,   // seller wallet
          createdAt: new Date().toISOString()
        };

        console.log('🔍 [Purchase] Setting activeTrade:', tradeData);
        setActiveTrade(tradeData);

        router.push(
          `?openChat=1&chatRoomId=${response.data.chatRoomId}`
        );

        console.log("Escrow purchase completed successfully!");
      }
    } catch (error: any) {
      console.error("Escrow purchase failed:", error);
      // Error handling is managed by the escrow operations hook
      // Additional context-specific errors can be shown here if needed
      if (error.message?.includes('backend') || error.message?.includes('server')) {
        showEscrowError(errorManager, "Server error occurred while preparing the purchase. Please try again.", 'buy');
      }
    } finally {
      setLoading(false);
    }
  };

    const handlePurchase = async () => {
    if (!state.userBo || !perkData || !solanaWallet?.address) {
      console.error("Missing required data for purchase");
      return;
    }

    if (!isConnected || !solanaWallet) {
      console.error("Wallet not connected");
      return;
    }

    // Prevent multiple rapid clicks
    if (loading) {
      console.log("Purchase already in progress, ignoring request");
      return;
    }

    try {
      setLoading(true);
      console.log("Starting escrow purchase...");

      // Step 1: Prepare escrow transaction on backend
      const response = await buyPerk({
        userId: state.userBo.id,
        perkId: perkData.perkId,
        price: perkData.price,
        buyerWallet: solanaWallet.address,
        sellerWallet: perkData.user.privywallet
      });

      if (response.status === 200) {
        console.log("Escrow prepared:", response.data);
        const { tradeId, escrowTxData } = response.data;

        // Step 2: Sign and execute escrow transaction using the hook
        console.log("Signing escrow transaction...");
        const txId = await escrowOperations.executeBuy(
          escrowTxData as EscrowTxData,
          solanaWallet
        );

        // Step 3: Confirm transaction on backend
        console.log("Confirming escrow transaction...");
        await confirmEscrow({ tradeId, txId });

        // Step 4: Set trade data and navigate to chat
        const tradeData = {
          ...response.data,
          status: 'escrowed',
          txId,
          // Extract necessary fields from escrowTxData for easy access
          escrowId: response.data.escrowTxData?.escrowId,
          from: response.data.escrowTxData?.buyerWallet, // buyer wallet
          to: response.data.escrowTxData?.sellerWallet,   // seller wallet
          createdAt: new Date().toISOString()
        };

        console.log('🔍 [Purchase] Setting activeTrade:', tradeData);
        setActiveTrade(tradeData);

        router.push(
          `?openChat=1&chatRoomId=${response.data.chatRoomId}`
        );

        console.log("Escrow purchase completed successfully!");
      }
    } catch (error: any) {
      console.error("Escrow purchase failed:", error);

      // Show user-friendly error messages
      let errorMessage = "Purchase failed. Please try again.";

      if (error.message?.includes("Transaction already in progress")) {
        errorMessage = "A transaction is already in progress. Please wait for it to complete.";
      } else if (error.message?.includes("Transaction already processed")) {
        errorMessage = "This transaction has already been processed. Please refresh the page.";
      } else if (error.message?.includes("Insufficient funds")) {
        errorMessage = "Insufficient funds to complete the transaction.";
      } else if (error.message?.includes("Wallet not connected")) {
        errorMessage = "Please connect your wallet and try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Use error manager instead of alert
      showEscrowError(errorManager, errorMessage, 'buy', {
        walletAddress: solanaWallet?.address
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRelease = async () => {
    if (!activeTrade || !solanaWallet?.address || !perkData) return;

    if (!isConnected || !solanaWallet) {
      console.error("Wallet not connected");
      return;
    }

    // Prevent multiple rapid clicks
    if (loading) {
      console.log("Release already in progress, ignoring request");
      return;
    }

    try {
      setLoading(true);
      console.log("Starting escrow release...");
      console.log("🔍 [Release] activeTrade data:", {
        escrowId: activeTrade.escrowId,
        from: activeTrade.from,
        to: activeTrade.to,
        tradeId: activeTrade.tradeId,
        status: activeTrade.status
      });

      if (!activeTrade.escrowId) {
        throw new Error("Missing escrowId in activeTrade");
      }
      if (!activeTrade.from) {
        throw new Error("Missing buyer wallet (from) in activeTrade");
      }

      // Check if seller has enough perk tokens
      console.log("🔍 [Release] Checking seller's perk token balance...");

      // For now, let's try the release and handle the insufficient funds error
      // TODO: Add a mechanism for seller to buy perk tokens if they don't have enough

      // Sign and execute escrow sell transaction using the hook
      const txId = await escrowOperations.executeSell(
        activeTrade.escrowId,
        'So11111111111111111111111111111111111111112', // SOL mint
        perkData.token.tokenAddress, // Perk token mint
        activeTrade.to, // Seller wallet (current user - who is releasing)
        activeTrade.from, // Buyer wallet
        solanaWallet, // Seller's wallet for signing (corrected)
        false, // skipValidation
        activeTrade.tradeId // Pass tradeId for trade data fetching
      );

      // Update backend with release
      await releasePerk({
        tradeId: activeTrade.tradeId,
        sellerWallet: activeTrade.to // Current user (seller) wallet
      });

      setActiveTrade({ ...activeTrade, status: "released", txId });
      console.log("Escrow release completed successfully!");
    } catch (error: any) {
      console.error("Release failed:", error);

      // Show user-friendly error messages
      let errorMessage = "Release failed. Please try again.";

      if (error.message?.includes("Release transaction already in progress")) {
        errorMessage = "A release transaction is already in progress. Please wait for it to complete.";
      } else if (error.message?.includes("Release transaction already processed")) {
        errorMessage = "This release transaction has already been processed. Please refresh the page.";
      } else if (error?.message && error.message.includes("insufficient funds")) {
        errorMessage = `Release failed: The seller doesn't have enough perk tokens to complete this transaction.

The seller needs to buy perk tokens first using the bonding curve before they can release escrow payments.

Error details: ${error.message}`;
      } else if (error.message?.includes("Wallet not connected")) {
        errorMessage = "Please connect your wallet and try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Use error manager instead of alert
      showEscrowError(errorManager, errorMessage, 'sell', {
        walletAddress: solanaWallet?.address
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefund = async () => {
    if (!activeTrade || !solanaWallet?.address) return;

    if (!isConnected || !solanaWallet) {
      console.error("Wallet not connected");
      return;
    }

    // Prevent multiple rapid clicks
    if (loading) {
      console.log("Refund already in progress, ignoring request");
      return;
    }

    try {
      setLoading(true);
      console.log("Starting escrow refund...");

      // Sign and execute escrow refund transaction using the hook
      const txId = await escrowOperations.executeRefund(
        activeTrade.escrowId,
        'So11111111111111111111111111111111111111112', // SOL mint
        solanaWallet.address, // Buyer wallet
        solanaWallet
      );

      // Update backend with refund
      await refundPerk({
        tradeId: activeTrade.tradeId,
        buyerWallet: solanaWallet.address
      });

      setActiveTrade({ ...activeTrade, status: "refunded", txId });
      console.log("Escrow refund completed successfully!");
    } catch (error: any) {
      console.error("Refund failed:", error);

      // Show user-friendly error messages
      let errorMessage = "Refund failed. Please try again.";

      if (error.message?.includes("Refund transaction already in progress")) {
        errorMessage = "A refund transaction is already in progress. Please wait for it to complete.";
      } else if (error.message?.includes("Refund transaction already processed")) {
        errorMessage = "This refund transaction has already been processed. Please refresh the page.";
      } else if (error.message?.includes("Insufficient funds")) {
        errorMessage = "Insufficient funds to complete the refund transaction.";
      } else if (error.message?.includes("Wallet not connected")) {
        errorMessage = "Please connect your wallet and try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Use error manager instead of alert
      showEscrowError(errorManager, errorMessage, 'refund', {
        walletAddress: solanaWallet?.address
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInitiateDispute = (role: 'buyer' | 'seller') => {
    setDisputeInitiatorRole(role);
    setDisputeModalOpen(true);
  };

  const handleDisputeInitiated = (disputeData: { disputeId: number; status: string }) => {
    console.log('Dispute initiated:', disputeData);
    // Update the active trade to reflect the dispute
    if (activeTrade) {
      setActiveTrade({
        ...activeTrade,
        disputeStatus: 'open',
        dispute: {
          id: disputeData.disputeId,
          status: disputeData.status
        }
      });
    }
    // You might want to show a success message here
  };

  // Handle message seller (pre-purchase chat)
  const handleMessageSeller = async () => {
    if (!state.userBo || !perkData) return;

    try {
      const token = localStorage.getItem("token");
      const response = await axios.post(
        `${API_CONFIG.BASE_URL}/messages/initiate`,
        {
          buyerId: state.userBo.id,
          sellerId: perkData.user.id,
          perkId: perkData.perkId
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.status === 200) {
        setChatRoomId(response.data.chatRoomId);
        setIsChatOpen(true);
      }
    } catch (error) {
      console.error("Failed to initiate chat:", error);
    }
  };

  // Handle report trade
  const handleReportTrade = async () => {
    if (!activeTrade) return;

    const reportReason = prompt("Please specify the reason for reporting this trade:");
    if (!reportReason) return;

    const reportDetails = prompt("Please provide additional details (optional):");

    try {
      await reportTrade({
        tradeId: activeTrade.tradeId,
        reportReason,
        reportDetails: reportDetails || undefined
      });

      alert("Trade has been reported successfully. Admin will review the case.");
      setActiveTrade({ ...activeTrade, status: "disputed" });
    } catch (error) {
      console.error("Report failed:", error);
      alert("Failed to report trade. Please try again.");
    }
  };

  const shortenAddress = (address: string) => {
    if (!address) return "";
    const start = address.slice(0, 3);
    const end = address.slice(-3);
    return `${start}...${end}`;
  };

  if (loading || !perkData || !perkData.token || !perkData.token.tokenAddress || !perkData.price) {
    return (
      <div className="min-h-screen bg-gray-50">
        <TokenDetailsLoading
          message="Loading perk details..."
          size="lg"
          className="min-h-screen"
        />
      </div>
    );
  }

  return (
    <div className="flex justify-center md:px-6 py-8 w-full">
      {/* Chat Modal: open if isChatOpen is true */}
     {isChatOpen && chatRoomId && (
        <ChatModal
          chatRoomId={chatRoomId}
          buyerId={state.userBo?.id || 0}
          sellerId={perkData?.user?.id || 0}
          onClose={() => setIsChatOpen(false)}
          onRelease={handleRelease}
          onRefund={handleRefund}
          onReport={handleReportTrade}
          onInitiateDispute={handleInitiateDispute}
          activeTrade={activeTrade}
        />
      )}
      <div className="flex flex-col xl:flex-row gap-8 md:gap-12 lg:gap-16 xl:gap-8 w-full mx-auto">
        <div className="flex-[975] min-w-[300px] lg:basis-[975px]">
          <div className="mb-8">
            <Detail
              productName={perkData.name}
              imageUrl={perkData.image}
              category="Perks"
              backgroundColor="#E5E5E5"
            />
          </div>

          <div className="mb-8">
            <TokenStats
              creator={shortenAddress(perkData.user.privywallet)}
              symbols={[perkData.token.ticker + '/SOL']}
              stats={{
                usd: 3425524,
                min24h: 0,
                max24h: 0,
                volume24h: 0,
                return24h: 0,
              }} />
          </div>

          <div className="mb-8">
            <TradeChart />
          </div>
        </div>

        <div className="flex-[675] min-w-[250px] lg:basis-[675px] max-w-full md:px-4">
          <div className="mb-8 mt-0 xl:mt-8 flex col justify-center">
            <ProductCard
              id={perkData.perkId}
              currency="$"
              bonusTokens={bonusTokens}
              title={perkData.name}
              description={perkData.description}
              price={perkData.price}
              seller={{ name: shortenAddress(perkData.user.privywallet), verified: true }}
              tokenName={perkData.token.name}
              tokenAddress={perkData.token.tokenAddress}
              creatorWallet={perkData.token.creatorWallet}
              tokenInfo={{...perkData.token, sellerId: perkData.user.id}}
              onPurchase={handlePurchase}
              isPurchased={!!activeTrade}
              tradeStatus={activeTrade?.status}
            />
          </div>

          <div className="mt-8 xl:mt-18">
            <Review perkId={perkData.perkId} reviews={perkData.reviews} canPost={perkData.hasPerk} />
          </div>

          {/* Trade Management Section */}
          {activeTrade && (
            <div className="mt-8 bg-white rounded-xl shadow-sm border border-slate-200 p-6">
              <h3 className="text-xl font-semibold text-slate-900 mb-6">Escrow Trade Management</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-slate-50 rounded-lg p-4">
                  <p className="text-sm font-medium text-slate-600 mb-2">Trade Status</p>
                  <span className={`inline-flex px-3 py-1.5 rounded-full text-sm font-medium capitalize ${
                    activeTrade.status === 'escrowed' ? 'bg-orange-100 text-[#FF6600] border border-orange-200' :
                    activeTrade.status === 'completed' ? 'bg-orange-100 text-[#FF6600] border border-orange-200' :
                    activeTrade.status === 'refunded' ? 'bg-red-100 text-red-700 border border-red-200' :
                    'bg-slate-100 text-slate-700 border border-slate-200'
                  }`}>
                    {activeTrade.status}
                  </span>
                </div>
                <div className="bg-slate-50 rounded-lg p-4">
                  <p className="text-sm font-medium text-slate-600 mb-2">Dispute Status</p>
                  <span className={`inline-flex px-3 py-1.5 rounded-full text-sm font-medium ${
                    getDisputeStatusInfo(activeTrade.disputeStatus || 'none').bgColor
                  } ${getDisputeStatusInfo(activeTrade.disputeStatus || 'none').color} border ${
                    activeTrade.disputeStatus === 'open' ? 'border-amber-200' :
                    activeTrade.disputeStatus === 'resolved' ? 'border-orange-200' :
                    'border-slate-200'
                  }`}>
                    {getDisputeStatusInfo(activeTrade.disputeStatus || 'none').label}
                  </span>
                </div>
                <div className="bg-slate-50 rounded-lg p-4">
                  <p className="text-sm font-medium text-slate-600 mb-2">Trade Amount</p>
                  <p className="text-lg font-semibold text-slate-900">${activeTrade.price || perkData.price}</p>
                </div>
              </div>

              {/* Trade Timeline */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-slate-900 mb-4">Trade Timeline</h4>
                <div className="bg-slate-50 rounded-lg p-4 space-y-4">
                  <div className="flex items-center">
                    <div className="flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full mr-4">
                      <div className="w-3 h-3 bg-[#FF6600] rounded-full"></div>
                    </div>
                    <div className="flex-1">
                      <span className="text-slate-700 font-medium">Payment locked in escrow</span>
                      <div className="text-sm text-slate-500">
                        {new Date(activeTrade.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>

                  {activeTrade.status === 'completed' && (
                    <div className="flex items-center">
                      <div className="flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full mr-4">
                        <div className="w-3 h-3 bg-[#FF6600] rounded-full"></div>
                      </div>
                      <div className="flex-1">
                        <span className="text-slate-700 font-medium">Payment released to seller</span>
                        <div className="text-sm text-slate-500">
                          {activeTrade.completedAt && new Date(activeTrade.completedAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTrade.disputeStatus === 'open' && (
                    <div className="flex items-center">
                      <div className="flex items-center justify-center w-8 h-8 bg-amber-100 rounded-full mr-4">
                        <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                      </div>
                      <div className="flex-1">
                        <span className="text-slate-700 font-medium">Dispute initiated</span>
                        <div className="text-sm text-slate-500">Pending review</div>
                      </div>
                    </div>
                  )}

                  {activeTrade.disputeStatus === 'resolved' && (
                    <div className="flex items-center">
                      <div className="flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full mr-4">
                        <div className="w-3 h-3 bg-[#FF6600] rounded-full"></div>
                      </div>
                      <div className="flex-1">
                        <span className="text-slate-700 font-medium">Dispute resolved by moderator</span>
                        <div className="text-sm text-slate-500">Completed</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Operation Status Indicators */}
              {escrowOperations.isAnyOperationPending && (
                <div className="space-y-2">
                  {escrowOperations.getOperationStatus('buy') === 'pending' && (
                    <EscrowOperationStatus
                      operation="buy"
                      status="pending"
                      showDetails={false}
                    />
                  )}
                  {escrowOperations.getOperationStatus('sell') === 'pending' && (
                    <EscrowOperationStatus
                      operation="sell"
                      status="pending"
                      showDetails={false}
                    />
                  )}
                  {escrowOperations.getOperationStatus('refund') === 'pending' && (
                    <EscrowOperationStatus
                      operation="refund"
                      status="pending"
                      showDetails={false}
                    />
                  )}
                  {escrowOperations.getOperationStatus('dispute') === 'pending' && (
                    <EscrowOperationStatus
                      operation="dispute"
                      status="pending"
                      showDetails={false}
                    />
                  )}
                </div>
              )}

              {/* User-Friendly Error Display */}
              {Object.entries(escrowOperations.operations).map(([operation, state]) =>
                state.error && (
                  <div key={operation} className="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3 flex-1">
                        <h3 className="text-sm font-medium text-red-800">
                          {operation === 'buy' && 'Purchase Failed'}
                          {operation === 'sell' && 'Payment Release Failed'}
                          {operation === 'refund' && 'Refund Failed'}
                          {operation === 'dispute' && 'Dispute Initiation Failed'}
                        </h3>
                        <div className="mt-2 text-sm text-red-700">
                          <p>
                            {operation === 'buy' && 'We encountered an issue processing your purchase. Please check your wallet connection and try again.'}
                            {operation === 'sell' && 'Unable to release payment at this time. Please ensure all conditions are met and try again.'}
                            {operation === 'refund' && 'The refund could not be processed. Please contact support if this issue persists.'}
                            {operation === 'dispute' && 'Unable to initiate dispute. Please try again or contact support for assistance.'}
                          </p>
                        </div>
                        <div className="mt-4 flex space-x-3">
                          <button
                            onClick={() => escrowOperations.retryOperation(operation as any)}
                            className="bg-[#FF6600] px-3 py-1.5 rounded-md text-sm font-medium text-white hover:bg-[#E55A00] transition-colors"
                          >
                            Try Again
                          </button>
                          <button
                            onClick={() => escrowOperations.clearOperation(operation as any)}
                            className="bg-white px-3 py-1.5 rounded-md text-sm font-medium text-red-600 border border-red-300 hover:bg-red-50 transition-colors"
                          >
                            Dismiss
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              )}

              {/* Trade Actions */}
              <div className="space-y-4">
                {/* Communication */}
                <div className="flex gap-3">
                  <button
                    onClick={() => setIsChatOpen(true)}
                    className="px-6 py-3 bg-[#FF6600] text-white rounded-lg hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-opacity-50 focus:ring-offset-2 flex items-center gap-2 font-medium transition-all duration-200"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a9.863 9.863 0 01-4.906-1.289L3 21l1.289-5.094A9.863 9.863 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                    </svg>
                    Open Chat
                  </button>
                </div>

                {/* Primary Actions */}
                <div className="flex flex-wrap gap-3">
                  {/* Seller Actions - Seller releases payment after providing service */}
                  {activeTrade.status === 'escrowed' && solanaWallet?.address === activeTrade.to && (
                    <>
                      <EscrowLoadingButton
                        onClick={handleRelease}
                        disabled={loading || escrowOperations.isAnyOperationPending}
                        loading={escrowOperations.getOperationStatus('sell') === 'pending'}
                        loadingText="Releasing..."
                        variant="success"
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        }
                      >
                        Release Payment
                      </EscrowLoadingButton>

                      {canInitiateDispute(activeTrade.status, activeTrade.createdAt, 2, activeTrade.disputeStatus).canDispute && (
                        <EscrowLoadingButton
                          onClick={() => handleInitiateDispute('buyer')}
                          disabled={escrowOperations.isAnyOperationPending}
                          loading={escrowOperations.getOperationStatus('dispute') === 'pending'}
                          loadingText="Initiating..."
                          variant="secondary"
                          className="bg-[#FF6600] hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-offset-2"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                          }
                        >
                          Initiate Dispute
                        </EscrowLoadingButton>
                      )}
                    </>
                  )}

                  {/* Buyer Actions - Buyer can refund if seller doesn't deliver */}
                  {activeTrade.status === 'escrowed' && solanaWallet?.address === activeTrade.from && (
                    <>
                      <EscrowLoadingButton
                        onClick={handleRefund}
                        disabled={loading || escrowOperations.isAnyOperationPending}
                        loading={escrowOperations.getOperationStatus('refund') === 'pending'}
                        loadingText="Processing Refund..."
                        variant="secondary"
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                          </svg>
                        }
                      >
                        Request Refund
                      </EscrowLoadingButton>

                      {canInitiateDispute(activeTrade.status, activeTrade.createdAt, 2, activeTrade.disputeStatus).canDispute && (
                        <EscrowLoadingButton
                          onClick={() => handleInitiateDispute('buyer')}
                          disabled={loading || escrowOperations.isAnyOperationPending}
                          loading={escrowOperations.getOperationStatus('dispute') === 'pending'}
                          loadingText="Initiating Dispute..."
                          variant="secondary"
                          className="bg-[#FF6600] hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-offset-2"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                          }
                        >
                          Initiate Dispute
                        </EscrowLoadingButton>
                      )}
                    </>
                  )}

                  {/* Seller Actions */}
                  {activeTrade.status === 'escrowed' && solanaWallet?.address === activeTrade.to && (
                    <>
                      <div className="p-6 bg-slate-50 border border-slate-200 rounded-lg">
                        <div className="flex items-start">
                          <div className="flex-shrink-0">
                            <div className="flex items-center justify-center w-10 h-10 bg-slate-100 rounded-lg">
                              <svg className="w-5 h-5 text-slate-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                              </svg>
                            </div>
                          </div>
                          <div className="ml-4">
                            <h4 className="text-slate-900 font-semibold text-lg">Seller Instructions</h4>
                            <p className="text-slate-700 mt-2 leading-relaxed">
                              As the seller, you need to have perk tokens in your wallet before you can release payment to yourself.
                              Make sure you have purchased enough perk tokens using the bonding curve first, then provide your service to the buyer.
                            </p>
                            <p className="text-slate-600 text-sm mt-3 bg-slate-100 rounded-md px-3 py-2">
                              💡 After providing your service/perk to the buyer, click "Release Payment" to complete the transaction.
                            </p>
                          </div>
                        </div>
                      </div>

                      {canInitiateDispute(activeTrade.status, activeTrade.createdAt, 2, activeTrade.disputeStatus).canDispute && (
                        <button
                          onClick={() => handleInitiateDispute('seller')}
                          className="px-6 py-3 bg-[#FF6600] text-white rounded-lg hover:bg-[#E55A00] focus:ring-2 focus:ring-[#FF6600] focus:ring-offset-2 flex items-center gap-2 font-medium transition-all duration-200 shadow-sm"
                          title="Disputes must be initiated within 2 days of purchase"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          Initiate Dispute
                        </button>
                      )}
                    </>
                  )}
                </div>

                {/* Status Messages */}
                {activeTrade.disputeStatus === 'open' && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <div>
                        <h4 className="text-sm font-medium text-yellow-800">Dispute in Progress</h4>
                        <p className="text-sm text-yellow-700">
                          A dispute has been initiated for this trade. A moderator will review the case and make a decision.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {activeTrade.disputeStatus === 'resolved' && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <div>
                        <h4 className="text-sm font-medium text-green-800">Dispute Resolved</h4>
                        <p className="text-sm text-green-700">
                          The dispute for this trade has been resolved by a moderator.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Dispute Information */}
              {activeTrade.dispute && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Dispute Information</h4>
                  <p className="text-sm text-gray-600">
                    Dispute ID: #{activeTrade.dispute.id}
                  </p>
                  <p className="text-sm text-gray-600">
                    Status: {activeTrade.dispute.status}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Dispute Modal */}
      {activeTrade && (
        <DisputeModal
          isOpen={disputeModalOpen}
          onClose={() => setDisputeModalOpen(false)}
          tradeId={activeTrade.tradeId || activeTrade.id}
          initiatorRole={disputeInitiatorRole}
          onDisputeInitiated={handleDisputeInitiated}
        />
      )}

      {/* Error and Loading Modals */}
      <errorManager.ErrorModalComponent />
      <errorManager.LoadingModalComponent />

      {/* Congratulations Modal */}
      <CongratulationsModal {...congratulationsModal.getModalProps()} />
    </div>
  );
}
