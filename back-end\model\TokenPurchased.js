const { Model, DataTypes } = require("sequelize");

class TokenPurchased extends Model {
    static initModel(sequelize) {
        return TokenPurchased.init(
            {
                id: {
                    type: DataTypes.BIGINT,
                    autoIncrement: true,
                    primaryKey: true,
                },
                userId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'users', // Table name in DB
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                tokenId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'tokens', // Table name in DB
                        key: 'tokenId',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                amount: {
                    type: DataTypes.FLOAT,
                    allowNull: false,
                    defaultValue: 0,
                },
                buyorsell: {
                    type: DataTypes.BOOLEAN,
                    allowNull: false,
                    defaultValue: true,
                },
                price: {
                    type: DataTypes.FLOAT,
                    allowNull: false,
                },
                dollorPrice: {
                    type: DataTypes.FLOAT,
                    allowNull: true,
                },
                from: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                to: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                extra: {
                    type: DataTypes.JSON,
                    allowNull: true,
                },
                hasH: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                transactionId: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                status: {
                    type: DataTypes.STRING(50),
                    allowNull: false,
                    defaultValue: 'pending',
                },
                perkId: {
                    type: DataTypes.BIGINT,
                    allowNull: false,
                    references: {
                        model: 'perks', // Table name in DB
                        key: 'perkId',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                escrowTxId: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                fulfillmentStatus: {
                    type: DataTypes.STRING(50),
                    allowNull: false,
                    defaultValue: 'pending', // pending, fulfilled, disputed
                },
                disputeStatus: {
                    type: DataTypes.STRING(50),
                    allowNull: false,
                    defaultValue: 'none', // none, open, resolved
                },
                createdAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    defaultValue: DataTypes.NOW,
                },
            },
            {
                sequelize,
                tableName: 'token_purchased',
                timestamps: true,
            }
        );
    }
}

module.exports = TokenPurchased;
