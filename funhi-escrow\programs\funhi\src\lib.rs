// fixes unexpected `cfg` errors
// check https://solana.stackexchange.com/questions/17777/unexpected-cfg-condition-value-solana
#![allow(unexpected_cfgs)]
use anchor_lang::prelude::*;
use anchor_spl::{
    associated_token::AssociatedToken,
    metadata::{
        create_metadata_accounts_v3, mpl_token_metadata::types::DataV2, CreateMetadataAccountsV3,
        Metadata,
    },
    token_interface::{self, Mint, MintTo, TokenAccount, TokenInterface, TransferChecked, CloseAccount},
};

mod account;
mod error;
use account::*;

//declare_id!("4t4vj9mQjv5aAxbzuvXTEnGTUAteNJRPjNJwwpFMsspz");
declare_id!("3GYb3ceCDhdCFkS4zCPgqW1vv1LcCNmiAJUXWJ8JpqDX");

#[program]
pub mod funhi {

    use crate::error::ErrorCode;

    use super::*;

    #[allow(clippy::too_many_arguments)]
    pub fn initialize(
        ctx: Context<Initialize>,
        initial_virtual_token_reserves: u64,
        initial_virtual_sol_reserves: u64,
        initial_real_token_reserves: u64,
        token_total_supply: u64,
        trading_fee_bps: u64,
        fee_recipient: Pubkey,
        first_buy_fee_sol: u64,
        graduation_threshold: u64,
    ) -> Result<()> {
        require_gt!(
            token_total_supply,
            initial_real_token_reserves,
            ErrorCode::InvalidTokenReserveConfiguration
        );
        ctx.accounts.global.set_inner(Global {
            authority: ctx.accounts.authority.key(),
            fee_recipient,
            initial_virtual_token_reserves,
            initial_virtual_sol_reserves,
            initial_real_token_reserves,
            token_total_supply,
            trading_fee_bps,
            graduation_threshold,
            first_buy_fee_sol,
        });
        Ok(())
    }

    pub fn create(
        ctx: Context<Create>,
        token_name: String,
        token_symbol: String,
        token_uri: String,
    ) -> Result<()> {
        msg!("Creating metadata account...");
        msg!(
            "Metadata account address: {}",
            &ctx.accounts.metadata_account.key()
        );

        // mint initial_real_token_reserves to bonding_curve_ata
        let signer_seeds: &[&[&[u8]]] = &[&[
            b"bonding_curve",
            ctx.accounts.mint.to_account_info().key.as_ref(),
            &[ctx.bumps.bonding_curve],
        ]];
        // Cross Program Invocation (CPI)
        // Invoking the create_metadata_account_v3 instruction on the token metadata program
        create_metadata_accounts_v3(
            CpiContext::new(
                ctx.accounts.token_metadata_program.to_account_info(),
                CreateMetadataAccountsV3 {
                    metadata: ctx.accounts.metadata_account.to_account_info(),
                    mint: ctx.accounts.mint.to_account_info(),
                    mint_authority: ctx.accounts.bonding_curve.to_account_info(),
                    update_authority: ctx.accounts.creator.to_account_info(),
                    payer: ctx.accounts.signer.to_account_info(),
                    system_program: ctx.accounts.system_program.to_account_info(),
                    rent: ctx.accounts.rent.to_account_info(),
                },
            )
            .with_signer(signer_seeds),
            DataV2 {
                name: token_name,
                symbol: token_symbol,
                uri: token_uri,
                seller_fee_basis_points: 0,
                creators: None,
                collection: None,
                uses: None,
            },
            false, // Is mutable
            false, // Update authority is creator not signer
            None,  // Collection details
        )?;

        msg!("Initializing bonding_curve");
        // initialize bonding_curve
        ctx.accounts.bonding_curve.set_inner(BondingCurve {
            mint: ctx.accounts.mint.key(),
            virtual_token_reserves: ctx.accounts.global.initial_virtual_token_reserves,
            virtual_sol_reserves: ctx.accounts.global.initial_virtual_sol_reserves,
            real_token_reserves: ctx.accounts.global.initial_real_token_reserves,
            real_sol_reserves: 0,
            token_total_supply: ctx.accounts.global.token_total_supply,
            complete: false,
        });

        let cpi_accounts = MintTo {
            mint: ctx.accounts.mint.to_account_info(),
            to: ctx.accounts.bonding_curve_ata.to_account_info(),
            authority: ctx.accounts.bonding_curve.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_context = CpiContext::new(cpi_program, cpi_accounts).with_signer(signer_seeds);
        token_interface::mint_to(cpi_context, ctx.accounts.global.initial_real_token_reserves)?;

        let creator_vault_amount = ctx
            .accounts
            .global
            .token_total_supply
            .checked_sub(ctx.accounts.global.initial_real_token_reserves)
            .unwrap();

        // mint token_total_supply - initial_real_token_reserves to creator vault
        let cpi_accounts = MintTo {
            mint: ctx.accounts.mint.to_account_info(),
            to: ctx.accounts.creator_vault_ata.to_account_info(),
            authority: ctx.accounts.bonding_curve.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_context = CpiContext::new(cpi_program, cpi_accounts).with_signer(signer_seeds);
        token_interface::mint_to(cpi_context, creator_vault_amount)?;

        // initialize creator_vault
        ctx.accounts.creator_vault.set_inner(CreatorVault {
            deposited_amount: creator_vault_amount,
            withdrawn_amount: 0,
            owner: ctx.accounts.creator.key(),
            mint: ctx.accounts.mint.key(),
            creation_ts: Clock::get()?.unix_timestamp,
        });

        Ok(())
    }

    pub fn buy(ctx: Context<Buy>, sol_amount: u64, min_token_output: u64) -> Result<()> {
        let curve = &mut ctx.accounts.bonding_curve;
        require!(!curve.complete, ErrorCode::BondingCurveComplete);
        require_gt!(sol_amount, 0);

        let is_first_buy = curve.real_sol_reserves == 0;
        let fee: u64;

        // On the first buy, charge the fixed SOL fee
        if is_first_buy {
            fee = ctx.accounts.global.first_buy_fee_sol;
            require!(sol_amount >= fee, ErrorCode::InsufficientSolForFirstBuy);
        } else {
            // On all subsequent buys, charge the percentage-based trading fee
            fee = sol_amount
                .checked_mul(ctx.accounts.global.trading_fee_bps)
                .and_then(|res| res.checked_div(10000))
                .ok_or(ProgramError::ArithmeticOverflow)?;
        }

        let sol_amount_after_fee = sol_amount
            .checked_sub(fee)
            .ok_or(ProgramError::ArithmeticOverflow)?;

        // Calculate token output using the constant product formula
        let k = u128::from(curve.virtual_sol_reserves)
            .checked_mul(u128::from(curve.virtual_token_reserves))
            .ok_or(ProgramError::ArithmeticOverflow)?;

        let new_virtual_sol_reserves = curve
            .virtual_sol_reserves
            .checked_add(sol_amount_after_fee)
            .ok_or(ProgramError::ArithmeticOverflow)?;

        let new_virtual_token_reserves = k
            .checked_div(u128::from(new_virtual_sol_reserves))
            .ok_or(ProgramError::ArithmeticOverflow)?;

        let tokens_out = curve
            .virtual_token_reserves
            .checked_sub(new_virtual_token_reserves as u64)
            .ok_or(ProgramError::ArithmeticOverflow)?;

        require_gte!(tokens_out, min_token_output);
        require_gte!(curve.real_token_reserves, tokens_out);

        // State Updates
        curve.virtual_sol_reserves = new_virtual_sol_reserves;
        curve.virtual_token_reserves = new_virtual_token_reserves as u64;
        curve.real_sol_reserves = curve
            .real_sol_reserves
            .checked_add(sol_amount_after_fee)
            .ok_or(ProgramError::ArithmeticOverflow)?;
        curve.real_token_reserves = curve
            .real_token_reserves
            .checked_sub(tokens_out)
            .ok_or(ProgramError::ArithmeticOverflow)?;

        // SOL Transfers (CPIs)
        let fee_transfer_cpi_context = CpiContext::new(
            ctx.accounts.system_program.to_account_info(),
            anchor_lang::system_program::Transfer {
                from: ctx.accounts.signer.to_account_info(),
                to: ctx.accounts.fee_recipient.to_account_info(),
            },
        );
        anchor_lang::system_program::transfer(fee_transfer_cpi_context, fee)?;

        let sol_transfer_cpi_context = CpiContext::new(
            ctx.accounts.system_program.to_account_info(),
            anchor_lang::system_program::Transfer {
                from: ctx.accounts.signer.to_account_info(),
                to: curve.to_account_info(),
            },
        );
        anchor_lang::system_program::transfer(sol_transfer_cpi_context, sol_amount_after_fee)?;

        // Token Transfer (CPI)
        let seeds: &[&[&[u8]]] = &[&[
            b"bonding_curve",
            ctx.accounts.mint.to_account_info().key.as_ref(),
            &[ctx.bumps.bonding_curve],
        ]];
        let decimals = ctx.accounts.mint.decimals;
        let cpi_accounts = TransferChecked {
            mint: ctx.accounts.mint.to_account_info(),
            from: ctx.accounts.bonding_curve_ata.to_account_info(),
            to: ctx.accounts.user_ata.to_account_info(),
            authority: curve.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_context = CpiContext::new(cpi_program, cpi_accounts).with_signer(seeds);
        token_interface::transfer_checked(cpi_context, tokens_out, decimals)?;

        // Check for graduation
        if curve.real_sol_reserves >= ctx.accounts.global.graduation_threshold {
            curve.complete = true;
            msg!("Bonding curve has graduated!");
        }

        Ok(())
    }

    pub fn sell(ctx: Context<Sell>, token_amount: u64, min_sol_output: u64) -> Result<()> {
        let curve = &mut ctx.accounts.bonding_curve;
        require!(!curve.complete, ErrorCode::BondingCurveComplete);
        require_gt!(token_amount, 0);

        // Calculate SOL output
        let k = u128::from(curve.virtual_sol_reserves)
            .checked_mul(u128::from(curve.virtual_token_reserves))
            .ok_or(ProgramError::ArithmeticOverflow)?;
        let new_virtual_token_reserves = curve
            .virtual_token_reserves
            .checked_add(token_amount)
            .ok_or(ProgramError::ArithmeticOverflow)?;
        let new_virtual_sol_reserves = k
            .checked_div(u128::from(new_virtual_token_reserves))
            .ok_or(ProgramError::ArithmeticOverflow)?;
        let sol_out_gross = curve
            .virtual_sol_reserves
            .checked_sub(new_virtual_sol_reserves as u64)
            .ok_or(ProgramError::ArithmeticOverflow)?;

        // Calculate fee
        let fee = sol_out_gross
            .checked_mul(ctx.accounts.global.trading_fee_bps)
            .and_then(|res| res.checked_div(10000))
            .ok_or(ProgramError::ArithmeticOverflow)?;
        let sol_out_net = sol_out_gross
            .checked_sub(fee)
            .ok_or(ProgramError::ArithmeticOverflow)?;

        // Validation
        require_gte!(sol_out_net, min_sol_output);
        require_gte!(curve.real_sol_reserves, sol_out_gross);

        // State Updates
        curve.virtual_sol_reserves = new_virtual_sol_reserves as u64;
        curve.virtual_token_reserves = new_virtual_token_reserves;
        curve.real_sol_reserves = curve
            .real_sol_reserves
            .checked_sub(sol_out_gross)
            .ok_or(ProgramError::ArithmeticOverflow)?;
        curve.real_token_reserves = curve
            .real_token_reserves
            .checked_add(token_amount)
            .ok_or(ProgramError::ArithmeticOverflow)?;

        // Token Transfer (CPI) - This part is correct and remains the same.
        let decimals = ctx.accounts.mint.decimals;
        let cpi_accounts = TransferChecked {
            mint: ctx.accounts.mint.to_account_info(),
            from: ctx.accounts.user_ata.to_account_info(),
            to: ctx.accounts.bonding_curve_ata.to_account_info(),
            authority: ctx.accounts.signer.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_context = CpiContext::new(cpi_program, cpi_accounts);
        token_interface::transfer_checked(cpi_context, token_amount, decimals)?;

        // SOL Transfers
        // Debit the total gross amount from the bonding curve
        ctx.accounts.bonding_curve.sub_lamports(sol_out_gross)?;

        // Credit the fee to the fee recipient
        ctx.accounts.fee_recipient.add_lamports(fee)?;

        // Credit the net proceeds to the user (signer)
        ctx.accounts.signer.add_lamports(sol_out_net)?;

        Ok(())
    }

    pub fn withdraw_unlocked(ctx: Context<WithdrawUnlocked>) -> Result<()> {
        let vault = &mut ctx.accounts.creator_vault;
        require_keys_eq!(
            vault.owner,
            ctx.accounts.creator.key(),
            ErrorCode::Unauthorized
        );

        const ONE_DAY_IN_SECONDS: i64 = 86400;
        const UNLOCK_PERIOD_DAYS: i64 = 100; // 1% per day for 100 days
        const TOTAL_VESTING_SECONDS: i64 = ONE_DAY_IN_SECONDS * UNLOCK_PERIOD_DAYS;

        let now_ts = Clock::get()?.unix_timestamp;
        let elapsed_time = now_ts
            .checked_sub(vault.creation_ts)
            .ok_or(ProgramError::ArithmeticOverflow)?;

        // If no time has passed or clock is weird, do nothing.
        if elapsed_time <= 0 {
            return Ok(());
        }

        // Calculate the total amount that should be vested by now
        let total_vested_amount = if elapsed_time >= TOTAL_VESTING_SECONDS {
            vault.deposited_amount // Fully vested
        } else {
            // Use u128 to prevent overflow during the multiplication
            (u128::from(vault.deposited_amount)
                .checked_mul(elapsed_time as u128)
                .ok_or(ProgramError::ArithmeticOverflow)?
                .checked_div(TOTAL_VESTING_SECONDS as u128)
                .ok_or(ProgramError::ArithmeticOverflow)?) as u64
        };

        // Calculate how much is available to withdraw now
        let available_to_withdraw = total_vested_amount
            .checked_sub(vault.withdrawn_amount)
            .ok_or(ProgramError::ArithmeticOverflow)?;

        require_gt!(available_to_withdraw, 0, ErrorCode::NothingToWithdraw);

        // Update state BEFORE the transfer
        vault.withdrawn_amount = vault
            .withdrawn_amount
            .checked_add(available_to_withdraw)
            .ok_or(ProgramError::ArithmeticOverflow)?;

        // Perform the CPI to transfer tokens from the vault's ATA to the creator's ATA
        let creator_key = ctx.accounts.creator.key();
        let mint_key = ctx.accounts.mint.key();
        let seeds: &[&[&[u8]]] = &[&[
            b"creator_vault".as_ref(),
            creator_key.as_ref(),
            mint_key.as_ref(),
            &[ctx.bumps.creator_vault],
        ]];
        let decimals = ctx.accounts.mint.decimals;

        let cpi_accounts = TransferChecked {
            mint: ctx.accounts.mint.to_account_info(),
            from: ctx.accounts.creator_vault_ata.to_account_info(),
            to: ctx.accounts.creator_destination_ata.to_account_info(),
            authority: ctx.accounts.creator_vault.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_context = CpiContext::new(cpi_program, cpi_accounts).with_signer(seeds);
        token_interface::transfer_checked(cpi_context, available_to_withdraw, decimals)?;

        Ok(())
    }

    pub fn withdraw(ctx: Context<Withdraw>) -> Result<()> {
        const GLOBAL_WITHDRAW_COOLDOWN_SECONDS: i64 = 10;
        let ledger = &mut ctx.accounts.last_withdraw;
        let now_ts = Clock::get()?.unix_timestamp;

        require!(
            now_ts
                .checked_sub(ledger.last_withdraw_timestamp)
                .unwrap_or(0)
                >= GLOBAL_WITHDRAW_COOLDOWN_SECONDS,
            ErrorCode::WithdrawalCooldownActive
        );

        let curve = &mut ctx.accounts.bonding_curve;
        require!(curve.complete, ErrorCode::BondingCurveNotComplete);

        // Withdraw all tokens from the bonding curve's ATA
        let token_balance = ctx.accounts.bonding_curve_ata.amount;
        if token_balance > 0 {
            let seeds: &[&[&[u8]]] = &[&[
                b"bonding_curve",
                ctx.accounts.mint.to_account_info().key.as_ref(),
                &[ctx.bumps.bonding_curve],
            ]];
            let decimals = ctx.accounts.mint.decimals;
            let cpi_accounts = TransferChecked {
                mint: ctx.accounts.mint.to_account_info(),
                from: ctx.accounts.bonding_curve_ata.to_account_info(),
                to: ctx.accounts.authority_ata.to_account_info(),
                authority: curve.to_account_info(),
            };
            let cpi_program = ctx.accounts.token_program.to_account_info();
            let cpi_context = CpiContext::new(cpi_program, cpi_accounts).with_signer(seeds);
            token_interface::transfer_checked(cpi_context, token_balance, decimals)?;
        }

        ledger.last_withdraw_timestamp = now_ts;

        // Withdraw all SOL from the bonding curve account, leaving exact rent.
        let rent = Rent::get()?;
        let rent_exempt_minimum = rent.minimum_balance(curve.to_account_info().data_len());
        let current_balance = curve.to_account_info().lamports();

        if current_balance > rent_exempt_minimum {
            let withdrawable_sol = current_balance
                .checked_sub(rent_exempt_minimum)
                .ok_or(ProgramError::ArithmeticOverflow)?;
            curve.sub_lamports(withdrawable_sol)?;
            ctx.accounts.authority.add_lamports(withdrawable_sol)?;
        }

        Ok(())
    }

    #[allow(clippy::too_many_arguments)]
    pub fn update_global_config(
        ctx: Context<UpdateGlobalConfig>,
        new_authority: Pubkey,
        new_fee_recipient: Pubkey,
        new_trading_fee_bps: u64,
        new_first_buy_fee_sol: u64,
        new_initial_virtual_token_reserves: u64,
        new_initial_virtual_sol_reserves: u64,
        new_initial_real_token_reserves: u64,
        new_token_total_supply: u64,
        new_graduation_threshold: u64,
    ) -> Result<()> {
        require!(new_trading_fee_bps <= 10000, ErrorCode::FeeTooHigh);
        require_gt!(
            new_token_total_supply,
            new_initial_real_token_reserves,
            ErrorCode::InvalidTokenReserveConfiguration
        );

        ctx.accounts.global.set_inner(Global {
            authority: new_authority,
            fee_recipient: new_fee_recipient,
            initial_virtual_token_reserves: new_initial_virtual_token_reserves,
            initial_virtual_sol_reserves: new_initial_virtual_sol_reserves,
            initial_real_token_reserves: new_initial_real_token_reserves,
            token_total_supply: new_token_total_supply,
            trading_fee_bps: new_trading_fee_bps,
            first_buy_fee_sol: new_first_buy_fee_sol,
            graduation_threshold: new_graduation_threshold,
        });

        Ok(())
    }

    // This instruction is only available for testing purposes
    pub fn set_creation_timestamp(
        ctx: Context<SetCreationTimestamp>,
        timestamp: i64,
    ) -> Result<()> {
        ctx.accounts.creator_vault.creation_ts = timestamp;
        Ok(())
    }
    
    pub fn escrow_buy(ctx: Context<EscrowBuy>,
                      id: u64,
                      purchase_token_amount: u64,
                      perk_token_amount: u64,
    ) -> Result<()> {
        // Escrow buy
        // this instruction allows a user to buy a perk token by depositing a purchase token
        // the purchase token is transferred to a vault (locked)
        // 
        // use case example
        // Alice Sells a perk P in return for amount a of token A
        // Bob is interested in P, clicks buy, and this instruction is exected, Bob's funds a gets 
        //      locked or transfered to the vault.
        msg!("=== ESCROW BUY DEBUG START ===");
        msg!("ID: {}", id);
        msg!("Purchase token amount: {}", purchase_token_amount);
        msg!("Perk token amount: {}", perk_token_amount);
    
        msg!("Signer: {}", ctx.accounts.signer.key());
        msg!("Purchase token mint: {}", ctx.accounts.purchase_token_mint.key());
        msg!("Perk token mint: {}", ctx.accounts.perk_token_mint.key());
    
        require!(purchase_token_amount > 0, ErrorCode::InvalidAmount);
        require!(perk_token_amount > 0, ErrorCode::InvalidAmount);
        // require that funds are sufficient for the purchase
        require!(ctx.accounts.purchase_token_ata.amount > purchase_token_amount, ErrorCode::InsufficientFunds);

        require!(ctx.accounts.purchase_token_mint.key() != ctx.accounts.perk_token_mint.key(), ErrorCode::InvalidMint);
        // transfer purchase_token_amount from buyer's ATA to the vault
        let cpi_accounts = TransferChecked {
            mint: ctx.accounts.purchase_token_mint.to_account_info(),
            from: ctx.accounts.purchase_token_ata.to_account_info(),
            to: ctx.accounts.vault.to_account_info(),
            authority: ctx.accounts.signer.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let seeds: &[&[&[u8]]] = &[&[
            b"purchase",
            &id.to_le_bytes()[..],
            &[ctx.bumps.purchase],
        ]];
        let cpi_context = CpiContext::new(cpi_program, cpi_accounts).with_signer(seeds);
        token_interface::transfer_checked(cpi_context, purchase_token_amount, ctx.accounts.purchase_token_mint.decimals)?;
        // save purchase details in the Purchase account
        ctx.accounts.purchase.set_inner(Purchase {
            id,
            buyer: ctx.accounts.signer.key(),
            purchase_token_mint: ctx.accounts.purchase_token_mint.key(),
            perk_token_mint: ctx.accounts.perk_token_mint.key(),
            perk_amount: perk_token_amount,
            bump: ctx.bumps.purchase,
        });
        msg!("=== ESCROW BUY DEBUG END ===");

        Ok(())
    }

    pub fn escrow_sell(ctx: Context<EscrowSell>,
        id:u64
    ) -> Result<()> {
        // Escrow Sell
        // this instruction for releasing the escrow.
        // the following happens upon completion, in an atomic manner: 
        // 1) withdraw  the offered tokens from the vault to the seller and closing the vault
        // 2) sending the purchased tokens from the seller to the buyer
        //
        // use case example:
        // after buy/escrow_buy instruction is executed, it's up to the buyer to release the 
        //      escrow once the perk is fullfilled.
        // in that case the buyer gets the perk, and the sellers gets the funds.

        // since the purchase account owns the vault, we will say
        // there is one signer (the purchase), with the seeds of the specific purchase amount
        // we can use these signer seeds to withdraw the token from the fault
        require!(ctx.accounts.purchase_token_mint.key() != ctx.accounts.perk_token_mint.key(), ErrorCode::InvalidMint);
        require!(ctx.accounts.purchase.perk_amount > 0, ErrorCode::InvalidAmount);
        let purchase_seeds: &[&[&[u8]]] = &[&[
            b"purchase",
            &ctx.accounts.purchase.id.to_le_bytes()[..],
            &[ctx.accounts.purchase.bump],
        ]];

        // withdraw the purchased tokens from the vault to the seller
        let cpi_accounts = TransferChecked {
            mint: ctx.accounts.perk_token_mint.to_account_info(),
            from: ctx.accounts.perk_token_ata.to_account_info(),
            to: ctx.accounts.perk_token_destination_ata.to_account_info(),
            authority: ctx.accounts.seller.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_context = CpiContext::new(cpi_program, cpi_accounts)
            .with_signer(purchase_seeds);
        token_interface::transfer_checked(cpi_context, ctx.accounts.purchase.perk_amount, ctx.accounts.perk_token_mint.decimals)?;



        // transfer the purchase token from the seller to the buyer
        let cpi_accounts = TransferChecked {
            mint: ctx.accounts.purchase_token_mint.to_account_info(),
            from: ctx.accounts.vault.to_account_info(),
            to: ctx.accounts.purchase_token_ata.to_account_info(),
            authority: ctx.accounts.purchase.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_context = CpiContext::new(cpi_program, cpi_accounts)
            .with_signer(purchase_seeds);
        token_interface::transfer_checked(cpi_context, ctx.accounts.vault.amount, ctx.accounts.purchase_token_mint.decimals)?;


        // Close the vault account
        let close_accounts = CloseAccount {
            account: ctx.accounts.vault.to_account_info(),
            destination: ctx.accounts.seller.to_account_info(),
            authority: ctx.accounts.purchase.to_account_info(),
        };
        let close_program = ctx.accounts.token_program.to_account_info();
        let close_context = CpiContext::new(close_program, close_accounts)
            .with_signer(purchase_seeds);
        token_interface::close_account(close_context)?;
        Ok(())
    }

  
    pub fn escrow_refund(ctx: Context<EscrowRefund>,
         id: u64
        ) -> Result<()> {
        // 1) returning the tokens from the vault to the buyer's account
        // 2) Closing the vault and returning the rent to the maker
        require!(ctx.accounts.purchase.perk_amount > 0, ErrorCode::InvalidAmount);
        let purchase_seeds: &[&[&[u8]]] = &[&[
            b"purchase",
            &ctx.accounts.purchase.id.to_le_bytes()[..],
            &[ctx.accounts.purchase.bump],
        ]];
        // return the tokens from the vault to the buyer's account
        let cpi_accounts = TransferChecked {
            mint: ctx.accounts.purchase_token_mint.to_account_info(),
            from: ctx.accounts.vault.to_account_info(),
            to: ctx.accounts.buyer_token_ata.to_account_info(),
            authority: ctx.accounts.purchase.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_context = CpiContext::new(cpi_program, cpi_accounts)
            .with_signer(purchase_seeds);
        token_interface::transfer_checked(cpi_context, ctx.accounts.vault.amount, ctx.accounts.purchase_token_mint.decimals)?;
        // Close the vault and return the rent to the maker
        let close_accounts = CloseAccount {
            account: ctx.accounts.vault.to_account_info(),
            destination: ctx.accounts.buyer.to_account_info(),
            authority: ctx.accounts.purchase.to_account_info(),
        };
        let close_program = ctx.accounts.token_program.to_account_info();
        let close_context = CpiContext::new(close_program, close_accounts)
            .with_signer(purchase_seeds);
        token_interface::close_account(close_context)?;

        Ok(())
    }

}

#[account]
#[derive(InitSpace)]
pub struct Purchase {
    pub id: u64, // Unique identifier for the purchase
    pub buyer: Pubkey, // The buyer's public key
    pub purchase_token_mint: Pubkey , // The mint of the offered token for the purchase
    pub perk_token_mint: Pubkey, // The mint of the token being purchased as a perk
    pub perk_amount: u64, // The amount of the perk token being offered
    pub bump: u8, // Bump seed for PDA
}

#[derive(Accounts)]
#[instruction(id: u64)]
pub struct EscrowBuy<'info> {
    pub associated_token_program: Program<'info, AssociatedToken>,
    pub token_program: Interface<'info, TokenInterface>,
    pub system_program: Program<'info, System>,
    #[account(mut)]
    pub signer: Signer<'info>, // The user who is buying the token
    #[account(mint::token_program = token_program)]
    pub purchase_token_mint: InterfaceAccount<'info, Mint>, // The token being purchased
    #[account(mint::token_program = token_program)]
    pub perk_token_mint: InterfaceAccount<'info, Mint>, // The token being offered as a perk
    #[account(
        mut,
        associated_token::mint = purchase_token_mint,
        associated_token::authority = signer,
        associated_token::token_program = token_program,
    )]
    pub purchase_token_ata: InterfaceAccount<'info, TokenAccount>, // The user's token account for the purchased token
    #[account(
        init,
        payer = signer,
        space = Purchase::DISCRIMINATOR.len() + Purchase::INIT_SPACE,
        seeds = [b"purchase", id.to_le_bytes().as_ref()],
        bump
    )]
    pub purchase: Account<'info, Purchase>, // The purchase account for the transaction
    #[account(
        init,
        payer = signer,
        associated_token::mint = purchase_token_mint,
        associated_token::authority = purchase,
        associated_token::token_program = token_program,
    )]
    pub vault: InterfaceAccount<'info, TokenAccount>, // The vault account for the purchase
}

#[derive(Accounts)]
#[instruction(id: u64)]
pub struct EscrowSell<'info> {
    pub associated_token_program: Program<'info, AssociatedToken>,
    pub token_program: Interface<'info, TokenInterface>,
    pub system_program: Program<'info, System>,
    #[account(mut)]
    pub seller: Signer<'info>, // The user who is selling the token
    #[account(mut)]
    pub buyer: SystemAccount<'info>, // The buyer's account
    pub purchase_token_mint: InterfaceAccount<'info, Mint>, // The token being purchased
    pub perk_token_mint: InterfaceAccount<'info, Mint>, // The token being offered as a perk
    #[account(
        init_if_needed,
        payer = seller,
        associated_token::mint = purchase_token_mint,
        associated_token::authority = seller,
        associated_token::token_program = token_program,
    )]
    pub purchase_token_ata: InterfaceAccount<'info, TokenAccount>, // The user's token account for the purchased token
    #[account(
        init_if_needed,
        payer = seller,
        associated_token::mint = perk_token_mint,
        associated_token::authority = seller,
        associated_token::token_program = token_program,
    )]
    pub perk_token_ata: InterfaceAccount<'info, TokenAccount>, // The buyer's token account for the perk token
    #[account(
        init_if_needed,
        payer = seller,
        associated_token::mint = perk_token_mint,
        associated_token::authority = buyer,
        associated_token::token_program = token_program,
    )]
    pub perk_token_destination_ata: InterfaceAccount<'info, TokenAccount>, // The user's token account for the perk token
    #[account(
        mut,
        close = seller,
        has_one = buyer,
        has_one = purchase_token_mint,
        seeds = [b"purchase", purchase.id.to_le_bytes().as_ref()],
        bump = purchase.bump
    )]
    purchase: Account<'info, Purchase>, // The purchase account for the transaction
    #[account(
        mut,
        associated_token::mint = purchase_token_mint,
        associated_token::authority = purchase,
        associated_token::token_program = token_program,
    )]
    pub vault: InterfaceAccount<'info, TokenAccount>, // The vault account for the purchase
}

#[derive(Accounts)]
#[instruction(id: u64)]
pub struct EscrowRelease<'info> {
    pub associated_token_program: Program<'info, AssociatedToken>,
    pub token_program: Interface<'info, TokenInterface>,
    pub system_program: Program<'info, System>,
    #[account(mut)]
    pub seller: SystemAccount<'info>, // The seller's account
    #[account(mut)]
    pub buyer: Signer<'info>, // The buyer who is releasing the escrow
    pub purchase_token_mint: InterfaceAccount<'info, Mint>, // The token being purchased
    pub perk_token_mint: InterfaceAccount<'info, Mint>, // The token being offered as a perk
    #[account(
        mut,
        associated_token::mint = purchase_token_mint,
        associated_token::authority = seller,
        associated_token::token_program = token_program,
    )]
    pub purchase_token_ata: InterfaceAccount<'info, TokenAccount>, // The seller's token account for the purchased token
    #[account(
        mut,
        associated_token::mint = perk_token_mint,
        associated_token::authority = purchase, // Seller must delegate authority to purchase PDA
        associated_token::token_program = token_program,
    )]
    pub perk_token_ata: InterfaceAccount<'info, TokenAccount>, // The seller's perk token account
    #[account(
        mut,
        associated_token::mint = perk_token_mint,
        associated_token::authority = buyer,
        associated_token::token_program = token_program,
    )]
    pub perk_token_destination_ata: InterfaceAccount<'info, TokenAccount>, // The buyer's perk token account
    #[account(
        mut,
        has_one = buyer,
        seeds = [b"purchase", purchase.id.to_le_bytes().as_ref()],
        bump = purchase.bump
    )]
    purchase: Account<'info, Purchase>, // The purchase account for the transaction
    #[account(
        mut,
        associated_token::mint = purchase_token_mint,
        associated_token::authority = purchase,
        associated_token::token_program = token_program,
    )]
    pub vault: InterfaceAccount<'info, TokenAccount>, // The vault account for the purchase
}

#[derive(Accounts)]
#[instruction(id: u64)]
pub struct EscrowRefund<'info> {
    pub token_program: Interface<'info, TokenInterface>,
    pub system_program: Program<'info, System>,
    #[account(mut)]
    pub buyer: Signer<'info>, // The buyer who is requesting the refund
    pub purchase_token_mint: InterfaceAccount<'info, Mint>, // The mint of the token being purchased
    #[account(
        mut,
        associated_token::mint = purchase_token_mint,
        associated_token::authority = buyer,
        associated_token::token_program = token_program,
    )]
    pub buyer_token_ata: InterfaceAccount<'info, TokenAccount>, // The buyer's token account for the purchased token
    #[account(
        mut,
        close = buyer,
        has_one = buyer,
        seeds = [b"purchase", purchase.id.to_le_bytes().as_ref()],
        bump = purchase.bump
    )]
    pub purchase: Account<'info, Purchase>, // The purchase account for the transaction
    #[account(
        mut,
        associated_token::mint = purchase_token_mint,
        associated_token::authority = purchase,
        associated_token::token_program = token_program,
    )]
    pub vault: InterfaceAccount<'info, TokenAccount>, // The vault account for the purchase
}

#[derive(Accounts)]
pub struct Initialize<'info> {
    #[account(mut)]
    pub authority: Signer<'info>,
    #[account(
        init,
        payer = authority,
        space = 8 + Global::INIT_SPACE,
        seeds = [b"global"], bump
    )]
    pub global: Account<'info, Global>,
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct Create<'info> {
    #[account(mut)]
    pub signer: Signer<'info>,
    /// CHECK: The creator's address is used as a seed for the mint PDA.
    pub creator: UncheckedAccount<'info>,
    #[account(constraint = global.fee_recipient == fee_recipient.key())]
    pub fee_recipient: SystemAccount<'info>,
    #[account(
        init,
        payer = signer,
        mint::decimals = 6,
        mint::authority = bonding_curve.key(),
    )]
    pub mint: InterfaceAccount<'info, Mint>,

    #[account()]
    pub global: Account<'info, Global>,
    #[account(
        init,
        payer = signer,
        space = 8 + BondingCurve::INIT_SPACE,
        seeds = [b"bonding_curve",mint.key().as_ref()], bump
    )]
    pub bonding_curve: Account<'info, BondingCurve>,
    #[account(
        init,
        payer = signer,
        associated_token::mint = mint,
        associated_token::authority = bonding_curve,
        associated_token::token_program = token_program,
    )]
    pub bonding_curve_ata: InterfaceAccount<'info, TokenAccount>,
    /// CHECK: Validate address by deriving pda
    #[account(
        mut,
        seeds = [b"metadata", token_metadata_program.key().as_ref(), mint.key().as_ref()],
        bump,
        seeds::program = token_metadata_program.key(),
    )]
    pub metadata_account: UncheckedAccount<'info>,
    #[account(
        init,
        payer = signer,
        space = 8 + CreatorVault::INIT_SPACE,
        seeds = [b"creator_vault".as_ref(), creator.key().as_ref(), mint.key().as_ref()], bump
    )]
    pub creator_vault: Account<'info, CreatorVault>,
    #[account(
        init,
        payer = signer,
        associated_token::mint= mint,
        associated_token::authority= creator_vault,
        associated_token::token_program = token_program,
    )]
    pub creator_vault_ata: InterfaceAccount<'info, TokenAccount>,
    pub token_metadata_program: Program<'info, Metadata>,
    pub associated_token_program: Program<'info, AssociatedToken>,
    pub token_program: Interface<'info, TokenInterface>,
    pub system_program: Program<'info, System>,
    pub rent: Sysvar<'info, Rent>,
}

#[derive(Accounts)]
pub struct Buy<'info> {
    #[account(mut)]
    pub signer: Signer<'info>,
    #[account(
        mut,
        seeds = [b"bonding_curve", mint.key().as_ref()],
        bump
    )]
    pub bonding_curve: Account<'info, BondingCurve>,
    #[account(
        mut,
        associated_token::mint = mint,
        associated_token::authority = bonding_curve
    )]
    pub bonding_curve_ata: InterfaceAccount<'info, TokenAccount>,
    #[account(
        init_if_needed,
        payer = signer,
        associated_token::mint = mint,
        associated_token::authority = signer
    )]
    pub user_ata: InterfaceAccount<'info, TokenAccount>,
    #[account(
        constraint = bonding_curve.mint == mint.key()
    )]
    pub mint: InterfaceAccount<'info, Mint>,
    /// CHECK: The creator's address is used as a seed for the mint PDA.
    pub creator: UncheckedAccount<'info>,
    #[account(
        seeds = [b"global"],
        bump
    )]
    pub global: Account<'info, Global>,
    #[account(mut, constraint = global.fee_recipient == fee_recipient.key())]
    pub fee_recipient: SystemAccount<'info>,
    pub system_program: Program<'info, System>,
    pub token_program: Interface<'info, TokenInterface>,
    pub associated_token_program: Program<'info, AssociatedToken>,
}

#[derive(Accounts)]
pub struct Sell<'info> {
    #[account(mut)]
    pub signer: Signer<'info>,
    #[account(
        mut,
        seeds = [b"bonding_curve", mint.key().as_ref()],
        bump
    )]
    pub bonding_curve: Account<'info, BondingCurve>,
    #[account(
        mut,
        associated_token::mint = mint,
        associated_token::authority = bonding_curve
    )]
    pub bonding_curve_ata: InterfaceAccount<'info, TokenAccount>,
    #[account(
        mut,
        associated_token::mint = mint,
        associated_token::authority = signer
    )]
    pub user_ata: InterfaceAccount<'info, TokenAccount>,
    #[account(
        constraint = bonding_curve.mint == mint.key()
    )]
    pub mint: InterfaceAccount<'info, Mint>,
    /// CHECK: The creator's address is used as a seed for the mint PDA.
    pub creator: UncheckedAccount<'info>,
    #[account(
        seeds = [b"global"],
        bump
    )]
    pub global: Account<'info, Global>,
    #[account(mut, constraint = global.fee_recipient == fee_recipient.key())]
    pub fee_recipient: SystemAccount<'info>,
    pub system_program: Program<'info, System>,
    pub token_program: Interface<'info, TokenInterface>,
}

#[derive(Accounts)]
pub struct WithdrawUnlocked<'info> {
    #[account(mut)]
    pub creator: Signer<'info>,
    #[account(
        mut,
        seeds = [b"creator_vault".as_ref(), creator.key().as_ref(), mint.key().as_ref()],
        bump,
        constraint = creator_vault.owner == creator.key()
    )]
    pub creator_vault: Account<'info, CreatorVault>,
    #[account(
        mut,
        associated_token::mint = mint,
        associated_token::authority = creator_vault
    )]
    pub creator_vault_ata: InterfaceAccount<'info, TokenAccount>,
    #[account(
        init_if_needed,
        payer = creator,
        associated_token::mint = mint,
        associated_token::authority = creator
    )]
    pub creator_destination_ata: InterfaceAccount<'info, TokenAccount>,
    #[account(
        constraint = creator_vault.mint == mint.key()
    )]
    pub mint: InterfaceAccount<'info, Mint>,
    pub system_program: Program<'info, System>,
    pub token_program: Interface<'info, TokenInterface>,
    pub associated_token_program: Program<'info, AssociatedToken>,
}

#[derive(Accounts)]
pub struct Withdraw<'info> {
    #[account(mut)]
    pub authority: Signer<'info>,
    #[account(
        seeds = [b"global"],
        bump,
        constraint = global.authority == authority.key()
    )]
    pub global: Account<'info, Global>,
    #[account(
        init_if_needed,
        payer = authority,
        space = 8 + LastWithdraw::INIT_SPACE,
        seeds = [b"last_withdraw"],
        bump
    )]
    pub last_withdraw: Account<'info, LastWithdraw>,
    #[account(
        constraint = bonding_curve.mint == mint.key()
    )]
    pub mint: InterfaceAccount<'info, Mint>,
    #[account(
        mut,
        seeds = [b"bonding_curve", mint.key().as_ref()],
        bump,
    )]
    pub bonding_curve: Account<'info, BondingCurve>,
    #[account(
        mut,
        associated_token::mint = mint,
        associated_token::authority = bonding_curve
    )]
    pub bonding_curve_ata: InterfaceAccount<'info, TokenAccount>,
    #[account(
        mut,
        associated_token::mint = mint,
        associated_token::authority = authority
    )]
    pub authority_ata: InterfaceAccount<'info, TokenAccount>,
    pub system_program: Program<'info, System>,
    pub token_program: Interface<'info, TokenInterface>,
    pub associated_token_program: Program<'info, AssociatedToken>,
    pub rent: Sysvar<'info, Rent>,
}

#[derive(Accounts)]
pub struct UpdateGlobalConfig<'info> {
    #[account(mut)]
    pub authority: Signer<'info>,
    #[account(
        mut,
        seeds = [b"global"],
        bump,
        constraint = global.authority == authority.key()
    )]
    pub global: Account<'info, Global>,
}

#[derive(Accounts)]
pub struct SetCreationTimestamp<'info> {
    // This should be signed by the program authority to be safe
    pub authority: Signer<'info>,
    #[account(
        seeds = [b"global"],
        bump,
        constraint = global.authority == authority.key()
    )]
    pub global: Account<'info, Global>,
    #[account(
        mut,
        seeds = [b"creator_vault".as_ref(), creator.key().as_ref(), mint.key().as_ref()],
        bump
    )]
    pub creator_vault: Account<'info, CreatorVault>,
    /// CHECK: The creator's address is used as a seed.
    pub creator: UncheckedAccount<'info>,
    #[account(
        constraint = creator_vault.mint == mint.key()
    )]
    pub mint: InterfaceAccount<'info, Mint>,
}
